package user_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"

	huser "sa/app/workspace/face/handlers/user"
	"sa/internal/user"
	"sa/pkg/ptrutil"
	"sa/testhelper"
)

func TestHandler_ListUserTokens(t *testing.T) {
	ss := testhelper.NewPack()
	h := huser.NewHandler(ss.WS, ss.US, ss.RS, ss.GS, ss.IS)

	uid, _ := ss.CreateRegularUser(ss.Ctx)
	ss.EngineFace.Use(func(c *gin.Context) {
		c.Set("uid", uid)
	})
	h.Register(ss.EngineFace.Group("/users"))

	w := httptest.NewRecorder()
	req, _ := http.NewRequestWithContext(ss.Ctx, http.MethodGet, fmt.Sprintf("/users/%d/tokens", uid), http.NoBody)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sa-user-id", strconv.Itoa(uid))

	ss.EngineFace.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)

	var rsp huser.UserTokenListRsp
	err := json.Unmarshal(w.Body.Bytes(), &rsp)
	require.NoError(t, err)
	require.Equal(t, 0, len(rsp.Data))
}

func TestHandler_CreateUserToken(t *testing.T) {
	ss := testhelper.NewPack()
	h := huser.NewHandler(ss.WS, ss.US, ss.RS, ss.GS, ss.IS)

	uid, _ := ss.CreateRegularUser(ss.Ctx)

	ss.EngineFace.Use(func(c *gin.Context) {
		c.Set("uid", uid)
	})
	h.Register(ss.EngineFace.Group("/users"))

	expireAt := time.Now().Add(24 * time.Hour)
	reqBody := user.UserTokenCreateReq{
		Name:        "Test Token",
		Description: ptrutil.Ptr("Test Description"),
		ExpireAt:    expireAt,
	}

	body, err := json.Marshal(reqBody)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	req, _ := http.NewRequestWithContext(ss.Ctx, http.MethodPost, fmt.Sprintf("/users/%d/tokens", uid), bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sa-user-id", strconv.Itoa(uid))

	ss.EngineFace.ServeHTTP(w, req)
	require.Equal(t, http.StatusCreated, w.Code)

	var rsp huser.UserTokenRsp
	err = json.Unmarshal(w.Body.Bytes(), &rsp)
	require.NoError(t, err)
	require.Equal(t, "Test Token", rsp.Name)
	require.Equal(t, "Test Description", *rsp.Description)
	require.Equal(t, "active", rsp.Status)
}

func TestHandler_CreateUserToken_InvalidExpireAt(t *testing.T) {
	ss := testhelper.NewPack()
	h := huser.NewHandler(ss.WS, ss.US, ss.RS, ss.GS, ss.IS)

	uid, _ := ss.CreateRegularUser(ss.Ctx)

	ss.EngineFace.Use(func(c *gin.Context) {
		c.Set("uid", uid)
	})
	h.Register(ss.EngineFace.Group("/users"))

	expireAt := time.Now().Add(-24 * time.Hour)
	reqBody := user.UserTokenCreateReq{
		Name:     "Test Token",
		ExpireAt: expireAt,
	}

	body, err := json.Marshal(reqBody)
	require.NoError(t, err)
	w := httptest.NewRecorder()
	req, _ := http.NewRequestWithContext(ss.Ctx, http.MethodPost, fmt.Sprintf("/users/%d/tokens", uid), bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sa-user-id", strconv.Itoa(uid))

	ss.EngineFace.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}

func TestHandler_UpdateUserToken(t *testing.T) {
	ss := testhelper.NewPack()
	h := huser.NewHandler(ss.WS, ss.US, ss.RS, ss.GS, ss.IS)

	uid, _ := ss.CreateRegularUser(ss.Ctx)

	ss.EngineFace.Use(func(c *gin.Context) {
		c.Set("uid", uid)
	})
	h.Register(ss.EngineFace.Group("/users"))

	expireAt := time.Now().Add(24 * time.Hour)
	createReq := user.UserTokenCreateReq{
		Name:     "Test Token",
		ExpireAt: expireAt,
	}

	token, err := ss.US.CreateUserToken(ss.Ctx, uid, createReq)
	require.NoError(t, err)

	updateReq := user.UserTokenUpdateReq{
		Name:   ptrutil.Ptr("Updated Token"),
		Status: ptrutil.Ptr("revoked"),
	}

	body, err := json.Marshal(updateReq)
	require.NoError(t, err)
	w := httptest.NewRecorder()
	req, _ := http.NewRequestWithContext(ss.Ctx, http.MethodPut, fmt.Sprintf("/users/%d/tokens/%s", uid, token.ID), bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sa-user-id", strconv.Itoa(uid))

	ss.EngineFace.ServeHTTP(w, req)
	require.Equal(t, http.StatusOK, w.Code)

	var rsp huser.UserTokenRsp
	err = json.Unmarshal(w.Body.Bytes(), &rsp)
	require.NoError(t, err)
	require.Equal(t, "Updated Token", rsp.Name)
	require.Equal(t, "revoked", rsp.Status)
}

func TestHandler_UserTokens_AccessDenied(t *testing.T) {
	ss := testhelper.NewPack()
	h := huser.NewHandler(ss.WS, ss.US, ss.RS, ss.GS, ss.IS)

	uid1, _ := ss.CreateRegularUser(ss.Ctx)
	uid2, _ := ss.CreateRegularUser(ss.Ctx)

	ss.EngineFace.Use(func(c *gin.Context) {
		c.Set("uid", uid1)
	})
	h.Register(ss.EngineFace.Group("/users"))

	w := httptest.NewRecorder()
	req, _ := http.NewRequestWithContext(ss.Ctx, http.MethodGet, fmt.Sprintf("/users/%d/tokens", uid2), http.NoBody)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("sa-user-id", strconv.Itoa(uid1))

	ss.EngineFace.ServeHTTP(w, req)
	require.Equal(t, http.StatusBadRequest, w.Code)
}
