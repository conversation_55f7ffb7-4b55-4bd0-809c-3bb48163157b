package user

import (
	_ "embed"
	"errors"
	"net/http"
	"sa/internal/mfa"
	"sa/internal/organization"
	"sa/internal/workspace"
	"strconv"
	"strings"
	"time"

	"sa/internal/license"
	"sa/internal/rootlicense"
	"sa/pkg/ptrutil"

	"github.com/gin-gonic/gin"

	"sa/apperr"
	"sa/internal/authz"
	"sa/internal/group"
	"sa/internal/invite"
	"sa/internal/role"
	"sa/internal/user"
	"sa/pkg/httputil"
	"sa/pkg/jsonschema"
)

//go:embed schema_create.json
var schemaCreate string

//go:embed schema_update_status.json
var schemaStatusUpdate string

//go:embed schema_update.json
var schemaUpdate string

//go:embed schema_update_groups.json
var schemaUpdateGroups string

//go:embed schema_update_roles.json
var schemaUpdateRoles string

//go:embed schema_update_company_info.json
var schemaUpdateCompanyInfo string

//go:embed schema_create_user_token.json
var schemaCreateUserToken string

//go:embed schema_update_user_token.json
var schemaUpdateUserToken string

type Handler struct {
	Service             *user.Service
	RoleService         *role.Service
	WorkspaceService    *workspace.Service
	OrganizationService *organization.Service
	GroupService        group.Service
	InviteService       invite.Service
	MFA                 *mfa.Service
	apiValidator        jsonschema.APIValidator
}
type RoleRow struct {
	ID   int    `json:"id" default:"1"`
	Name string `json:"name" default:"my role"`
}
type GroupRow struct {
	ID   int    `json:"id" default:"1"`
	Name string `json:"name" default:"my group"`
}
type SearchRow struct {
	ID    int     `json:"id"`
	Name  string  `json:"name"`
	Photo *string `json:"photo,omitempty" default:"https://account.corezoid.com/avatars/0.jpg"`
	Color *string `json:"color,omitempty" default:"#000000"`
}

type RspLogin struct {
	ID         int    `json:"id" default:"1"`
	CreateTime int    `json:"create_time" default:"**********"`
	Login      string `json:"login" default:"<EMAIL>"`
	Type       int    `json:"type" default:"1"`
	TypeName   string `json:"type_name" default:""`
}

type UpdateStatusReq struct {
	// User status, available values: active/blocked
	Status string `json:"status" default:"blocked"`
}

type Update struct {
	Lang  *string `json:"lang,omitempty"`
	Name  string  `json:"name"`
	Photo *string `json:"photo,omitempty" default:"https://account.corezoid.com/avatars/0.jpg"`
	Color *string `json:"color,omitempty" default:"#000000"`
}

func NewHandler(workspaceService *workspace.Service, service *user.Service, roleService *role.Service, groupService group.Service,
	inviteService invite.Service) *Handler {
	h := &Handler{
		OrganizationService: organization.NewService(service.Base),
		Service:             service,
		InviteService:       inviteService,
		RoleService:         roleService,
		GroupService:        groupService,
		WorkspaceService:    workspaceService,
		MFA:                 mfa.NewService(service.Base),
	}
	h.apiValidator = jsonschema.NewAPIValidator()
	h.apiValidator.Add("create", schemaCreate)
	h.apiValidator.Add("update_status", schemaStatusUpdate)
	h.apiValidator.Add("update", schemaUpdate)
	h.apiValidator.Add("update_groups", schemaUpdateGroups)
	h.apiValidator.Add("update_roles", schemaUpdateRoles)
	h.apiValidator.Add("update_company_info", schemaUpdateCompanyInfo)
	h.apiValidator.Add("create_user_token", schemaCreateUserToken)
	h.apiValidator.Add("update_user_token", schemaUpdateUserToken)
	return h
}

func (h *Handler) Register(rg *gin.RouterGroup) {
	rg.GET("/:user_id", h.UserInfo)
	rg.GET("/check", h.Check)
	rg.PUT("/company_info", h.UpdateCompanyInfo)
	rg.PUT("/:user_id", h.Update)
	rg.GET("/:user_id/tokens", h.ListUserTokens)
	rg.POST("/:user_id/tokens", h.CreateUserToken)
	rg.PUT("/:user_id/tokens/:token_id", h.UpdateUserToken)
}

func (h *Handler) RegisterInWorkspace(rg *gin.RouterGroup) {
	rg.GET("", h.List)
	rg.GET("/search", h.Search)
	rg.GET("/search/", h.Search)
	rg.GET("/search/:query", h.Search)
	rg.GET("/:user_id", h.WorkspaceUser)
	rg.PUT("/:user_id", h.UpdateStatus)
	rg.DELETE("/:user_id", h.Delete)
	rg.PUT("/:user_id/roles", h.UpdateRoles)
	rg.PUT("/:user_id/groups", h.UpdateGroups)
}

type Rsp struct {
	Meta RspMeta   `json:"meta"`
	Data []RspItem `json:"data"`
}
type RspMeta struct {
	Total int `json:"total"`
}
type RspItem struct {
	ID           int         `json:"id"`
	Name         string      `json:"name"`
	LastEntrance *time.Time  `json:"last_entrance,omitempty"`
	Status       string      `json:"status"`
	Photo        *string     `json:"photo,omitempty"`
	Color        *string     `json:"color,omitempty"`
	CreatedAt    *time.Time  `json:"created_at,omitempty"`
	Roles        []RoleRow   `json:"roles"`
	Groups       *[]GroupRow `json:"groups,omitempty"`
	Logins       []RspLogin  `json:"logins"`
}

// List returns a list of all users in the workspace
// no required perms
// @Summary List all users
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param search query string false "search query"
// @Param roles query []int false "filter by role ids"
// @Param with query string false "rsp with groups,created_at fields"
// @Param sort_by query string false "sort by field, available values: name"
// @Param order_by query string false "order by asc or desc"
// @Param limit query int false "limit for lazy loading"
// @Param offset query int false "offset for lazy loading"
// @Success 200 {object} Rsp
// @Router /workspaces/{workspace_id}/users [get]
func (h *Handler) List(ctx *gin.Context) {
	wid := ctx.Value("wid").(int)
	uid := ctx.Value("uid").(int)
	with := ctx.Query("with")
	search, limit, offset, sortBy, orderBy := httputil.GetListQueries(ctx, "name")
	var rolesBin string
	roleIDs := make([]int, 0)
	if rolesBin = ctx.Query("roles"); rolesBin != "" {
		rolesIDsBin := strings.Split(rolesBin, ",")
		for _, r := range rolesIDsBin {
			roleInt, err := strconv.Atoi(r)
			apperr.Error(err)
			roleIDs = append(roleIDs, roleInt)
		}
	}

	rows, total := h.Service.List(ctx, uid, user.Filter{RoleIDs: roleIDs, Search: search, Types: []int{authz.TypeUser}}, wid, limit, offset, sortBy, orderBy)
	ids := make([]int, 0, len(rows))
	for _, item := range rows {
		ids = append(ids, item.ID)
	}
	roles := h.RoleService.ListByUserWS(ctx, wid, ids)
	groups := make([]group.MemberRow, 0)
	if strings.Contains(with, "groups") {
		groups = h.GroupService.ListByUserWS(ctx, wid, ids)
	}

	logins, err := h.Service.GetLogins(ctx, ids)
	apperr.Error(err)
	rsp := make([]RspItem, 0, len(rows))
	isCreatedAdd := strings.Contains(with, "created_at")
	for _, item := range rows {
		newItem := initRow(item, roles, groups, logins)
		if isCreatedAdd {
			newItem.CreatedAt = ptrutil.Ptr(item.CreatedAt)
		}
		rsp = append(rsp, newItem)

	}
	ctx.JSON(http.StatusOK, Rsp{Data: rsp, Meta: RspMeta{Total: total}})
}

// Search returns a list of users in the workspace by query fieild
// no required perms
// @Summary Search users by id/name/login
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param query path string true "query"
// @Param limit query int true "limit for lazy loading"
// @Success 200 {array} SearchRow
// @Router /workspaces/{workspace_id}/users/search/{query} [get]
func (h *Handler) Search(ctx *gin.Context) {
	wid := ctx.Value("wid").(int)
	uid := ctx.Value("uid").(int)
	query := ctx.Param("query")
	if query == "" {
		panic(apperr.New(apperr.ErrBadRequest, "the minimum length of the query field is 1 character"))
	}
	limit, err := strconv.Atoi(ctx.Query("limit"))
	apperr.Error(err, "requires correct limit field")

	rows := h.Service.Search(ctx, uid, wid, []int{authz.TypeUser}, query, limit, 0)

	rsp := make([]SearchRow, 0, len(rows))
	for _, item := range rows {
		rsp = append(rsp, SearchRow{
			ID:    item.ID,
			Name:  item.Name,
			Photo: item.Photo,
			Color: item.Color,
		})
	}
	ctx.JSON(http.StatusOK, rsp)
}

// WorkspaceUser returns user details
// no required perms
// @Summary Get user info
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param user_id path int true "user id"
// @Success 200 {object} RspItem
// @Router /workspaces/{workspace_id}/users/{user_id} [get]
func (h *Handler) WorkspaceUser(ctx *gin.Context) {
	wid := ctx.Value("wid").(int)
	uid := ctx.Value("uid").(int)
	userID, _ := strconv.Atoi(ctx.Param("user_id"))
	row, err := h.Service.Get(ctx, uid, wid, userID)
	apperr.Error(err)
	roles := h.RoleService.ListByUserWS(ctx, wid, []int{userID})
	logins, err := h.Service.GetLogins(ctx, []int{userID})
	apperr.Error(err)
	rsp := initRow(&row, roles, nil, logins)
	ctx.JSON(http.StatusOK, rsp)
}

type ShortRowPermItem struct {
	Type string   `json:"type" default:"control"`
	IDs  []string `json:"ids"`
}
type UserRow struct {
	ID               int        `json:"id"`
	Name             string     `json:"name"`
	LastEntrance     time.Time  `json:"last_entrance"`
	Photo            *string    `json:"photo,omitempty"`
	Color            *string    `json:"color,omitempty"`
	Is2fa            bool       `json:"is_2fa"`
	Logins           []RspLogin `json:"logins"`
	LicenseExpiresAt time.Time  `json:"license_expires_at"`
	// license status, available values: active,expiring,canceled
	LicenseStatus string `json:"license_status"`
	// status, available values: active,blocked, blocked_by_license,
	Status  string             `json:"status"`
	Perms   []ShortRowPermItem `json:"perms"`
	Options struct {
		DisableWorkspaceCreation bool `json:"disable_workspace_creation"`
	} `json:"options"`
}

// UserInfo returns user's data
// @Summary Get user's data
// @Tags Users
// @Security BasicAuth
// @Param user_id path int true "user_id, 'me or 0' for own data"
// @Accept json
// @Produce json
// @Success 200 {object} UserRow
// @Router /users/{user_id} [get]
func (h *Handler) UserInfo(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	userID, _ := strconv.Atoi(ctx.Param("user_id"))
	if userID == 0 {
		userID = uid
	}
	if userID != uid {
		if err := h.Service.AS.MustHave(ctx, uid, 0, authz.TypePermEnv, authz.PermEnvUsersView); err != nil {
			panic(err)
		}
	}
	rsp := h.user(ctx, userID)
	ctx.JSON(http.StatusOK, rsp)
}

func (h *Handler) user(ctx *gin.Context, uid int) UserRow {
	rsp := UserRow{}
	ui, err := h.Service.GetByID(ctx, uid)
	apperr.Error(err)
	sLogins, err := h.Service.GetLogins(ctx, []int{uid})
	apperr.Error(err)
	logins := make([]string, len(sLogins))
	for i, item := range sLogins {
		logins[i] = item.Login
	}
	rsp.Options.DisableWorkspaceCreation = h.OrganizationService.IsForbiddenToCreateWS(ctx, uid, logins)

	apperr.Error(h.InviteService.Confirm(ctx, uid, logins))
	rsp.ID = ui.ID
	rsp.Name = ui.Name
	rsp.Photo = ui.Photo
	rsp.Color = ui.Color
	rsp.Is2fa = ui.Is2fa
	if ui.LastEntrance != nil {
		rsp.LastEntrance = time.Unix(int64(*ui.LastEntrance), 0)
	}

	rsp.Logins = make([]RspLogin, len(logins))
	for i, item := range sLogins {
		rsp.Logins[i] = RspLogin{
			ID:         item.ID,
			CreateTime: item.CreateTime,
			Login:      item.Login,
			Type:       item.Type,
			TypeName:   authz.ParseAuthType(item.Type),
		}
	}
	_, err = h.Service.Base.License.IsValid()
	rsp.LicenseExpiresAt = h.Service.Base.License.Get().TimeToExpire
	switch {
	case errors.Is(err, rootlicense.ErrLicenseExpired):
		rsp.LicenseStatus = "canceled"
	case errors.Is(err, rootlicense.ErrLicenseExpiring):
		rsp.LicenseStatus = "expiring"
	default:
		rsp.LicenseStatus = string(license.StatusActive)
	}
	_, aWID := h.RoleService.AS.GetAdminInfo()
	perms := h.RoleService.ListPermsByUserInWSString(ctx, aWID, []int{uid})
	if len(perms) > 0 {
		for _, item := range perms[uid] {
			if item.Type != "sa.env" && item.Type != "sa.env.cloud" {
				continue
			}
			rsp.Perms = append(rsp.Perms, ShortRowPermItem{Type: item.Type, IDs: item.IDs})
		}
	}
	rsp.Status = ui.Status.String()
	return rsp
}

type CheckRow struct {
	RedirectURI *string `json:"redirect_uri,omitempty"`
	// license status, available values: active,expiring,canceled
	LicenseStatus string `json:"license_status"`
	// status, available values: active,blocked, blocked_by_license,
	Status string `json:"status"`
}

// Check returns user's statuses(DEPRECATED)
// no required perms
// @Summary Get user's own data
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Success 200 {object} CheckRow
// @Router /users/check [get]
func (h *Handler) Check(ctx *gin.Context) {
	rsp := CheckRow{LicenseStatus: string(license.StatusActive), Status: string(user.StatusActive)}
	ui, err := h.Service.GetByID(ctx, ctx.Value("uid").(int))
	apperr.Error(err)
	rsp.Status = ui.Status.String()
	redirect := false
	if !ui.Status {
		redirect = true
	}
	if _, err := h.Service.Base.License.IsValid(); err != nil {
		redirect = true
		switch {
		case errors.Is(err, rootlicense.ErrLicenseExpired):
			rsp.LicenseStatus = "expired"
		case errors.Is(err, rootlicense.ErrLicenseExpiring):
			rsp.LicenseStatus = "expiring"
		default:
			rsp.LicenseStatus = string(license.StatusActive)
		}
	}
	if redirect {
		rsp.RedirectURI = ptrutil.Ptr(h.Service.Cfg.URL + "/profile")
	}
	ctx.JSON(http.StatusOK, rsp)
}

func initRow(u *user.Row, roles []role.MemberRow, groups []group.MemberRow, logins []user.Login) RspItem {
	row := RspItem{
		ID:     u.ID,
		Name:   u.Name,
		Photo:  u.Photo,
		Color:  u.Color,
		Roles:  []RoleRow{},
		Logins: []RspLogin{},
	}
	row.Status = string(user.StatusActive)
	if u.RuleStatus != nil && *u.RuleStatus == authz.RelBlocked {
		row.Status = string(user.StatusBlocked)
	}
	for _, item := range roles {
		if item.UID == row.ID {
			row.Roles = append(row.Roles, RoleRow{ID: item.RID, Name: item.Name})
		}
	}
	for _, item := range groups {
		if item.UID == row.ID {
			if row.Groups == nil {
				row.Groups = &[]GroupRow{}
			}
			*row.Groups = append(*row.Groups, GroupRow{ID: item.GID, Name: item.Name})
		}
	}

	if u.LastEntrance != nil {
		row.LastEntrance = ptrutil.Ptr(time.Unix(int64(*u.LastEntrance), 0))
	}

	for _, login := range logins {
		if row.ID == login.UserID {
			row.Logins = append(row.Logins, RspLogin{
				ID:         login.ID,
				CreateTime: login.CreateTime,
				Login:      login.Login,
				Type:       login.Type,
			})
		}
	}

	return row
}

// UpdateStatus changes user status (active<->blocked)
// PermUserGroupManaging is required
// @Summary Update user status
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param user_id path int true "user_id"
// @Param Req body UpdateStatusReq true "body"
// @Success 200
// @Router /workspaces/{workspace_id}/users/{user_id} [put]
func (h *Handler) UpdateStatus(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	wid := ctx.Value("wid").(int)
	var req UpdateStatusReq
	apperr.ErrorWithCode(apperr.ErrBadRequest, ctx.BindJSON(&req))
	if err := h.apiValidator.Validate("update_status", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}
	updatedUID, _ := strconv.Atoi(ctx.Param("user_id"))
	var err error
	if req.Status == "block" || req.Status == "blocked" {
		err = h.Service.Block(ctx, uid, wid, updatedUID)
	} else {
		err = h.Service.UnBlock(ctx, uid, wid, updatedUID)
	}
	apperr.Error(err)

	ctx.Status(http.StatusOK)
}

// Update changes user
// PermUserGroupManaging is required
// @Summary Update user
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param user_id path int true "user_id"
// @Param Req body Update true "body"
// @Success 200
// @Router /users/{user_id} [put]
func (h *Handler) Update(ctx *gin.Context) {
	var req Update
	apperr.ErrorWithCode(apperr.ErrBadRequest, ctx.BindJSON(&req))

	if err := h.apiValidator.Validate("update", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}
	updatedUID, _ := strconv.Atoi(ctx.Param("user_id"))
	uid := ctx.Value("uid").(int)

	if updatedUID != uid {
		panic(apperr.New(apperr.ErrBadRequest))
	}

	err := h.Service.Update(ctx, updatedUID, req.Name, req.Photo, req.Color, req.Lang)
	apperr.Error(err)

	ctx.JSON(http.StatusOK, req)
}

// Delete removes the user from the workspace
// PermUserGroupManaging is required
// @Summary Remove from the workspace
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param user_id path int true "user_id"
// @Param new_owner_id query int false "new_owner_id"
// @Param obj_owner_ids query []int false "obj_owner_ids"
// @Success 200
// @Router /workspaces/{workspace_id}/users/{user_id} [delete]
func (h *Handler) Delete(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	wid := ctx.Value("wid").(int)
	deletedUID, _ := strconv.Atoi(ctx.Param("user_id"))
	newOwnerID, _ := strconv.Atoi(ctx.Query("new_owner_id"))
	objOwnerIDsBin := strings.Split(ctx.Query("obj_owner_ids"), ",")
	// to int
	objOwnerIDs := make([]int, 0, len(objOwnerIDsBin))

	for _, v := range objOwnerIDsBin {
		if v == "" {
			continue
		}
		dd, err := strconv.Atoi(v)
		apperr.Error(err, "invalid obj_owner_ids")
		objOwnerIDs = append(objOwnerIDs, dd)
	}
	groupAndAPINewOwner := newOwnerID
	if groupAndAPINewOwner == 0 {
		owners := h.RoleService.ListOwner(ctx, wid)
		groupAndAPINewOwner = owners[0]
	}

	// update group owner
	owners := h.GroupService.ListOwners(ctx, group.Filter{
		WID: wid,
		UID: deletedUID,
	})
	for gid, owner := range owners {
		isOwner := false
		for _, o := range owner {
			if o.ID == deletedUID {
				isOwner = true
				break
			}
		}
		if !isOwner {
			continue
		}
		err := h.GroupService.UpdateOwner(ctx, deletedUID, wid, gid, groupAndAPINewOwner, true)
		apperr.Error(err)
	}

	// update api owner
	h.Service.UpdateAPIOwner(ctx, wid, deletedUID, groupAndAPINewOwner)
	ui, err := h.Service.Get(ctx, 0, wid, newOwnerID)
	if err == nil && ui.RuleStatus != nil && *ui.RuleStatus == authz.RelBlocked {
		panic(apperr.ErrSpecifiedUserIsBlocked)
	}
	apperr.Error(h.WorkspaceService.Leave(ctx, uid, wid, deletedUID, newOwnerID, objOwnerIDs))

	ctx.Status(http.StatusOK)
}

type UpdateRole struct {
	// ID is role_id
	ID int `json:"id"  default:"1"`
	// Active is the marker to add or remove from the list
	Active bool `json:"active"`
}

// UpdateRoles add/remove user to/from the role
// @Summary add/remove user to/from the role
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param user_id path int true "user_id"
// @Param []UpdateRole body []UpdateRole true "body"
// @Success 200
// @Router /workspaces/{workspace_id}/users/{user_id}/roles [put]
func (h *Handler) UpdateRoles(ctx *gin.Context) {
	var req []UpdateRole
	err := ctx.BindJSON(&req)
	apperr.Error(err)
	if err := h.apiValidator.Validate("update_roles", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}
	wid := ctx.Value("wid").(int)
	uid := ctx.Value("uid").(int)
	userID, err := strconv.Atoi(ctx.Param("user_id"))
	apperr.Error(err, "wrong user_id")
	sreq := make([]role.UpdateUser, len(req))
	for i, item := range req {
		sreq[i] = role.UpdateUser{
			UserID: userID,
			RoleID: item.ID,
			Active: item.Active,
		}
	}
	err = h.RoleService.UpdateUsers(ctx, uid, wid, sreq)
	apperr.Error(err)
	ctx.Status(http.StatusOK)
}

type UpdateGroup struct {
	// ID is group_id
	ID int `json:"id"  default:"1"`
	// Active is the marker to add or remove from the list
	Active bool `json:"active"`
}

// UpdateGroups add/remove user to/from the group
// @Summary add/remove user to/from the group
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param user_id path int true "user_id"
// @Param []UpdateGroup body []UpdateGroup true "body"
// @Success 200
// @Router /workspaces/{workspace_id}/users/{user_id}/groups [put]
func (h *Handler) UpdateGroups(ctx *gin.Context) {
	var req []UpdateGroup
	apperr.Error(ctx.BindJSON(&req))
	if err := h.apiValidator.Validate("update_groups", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}
	wid := ctx.Value("wid").(int)
	uid := ctx.Value("uid").(int)
	userID, err := strconv.Atoi(ctx.Param("user_id"))
	apperr.Error(err, "wrong user_id")
	sreq := make([]group.UpdateUser, len(req))
	for i, item := range req {
		sreq[i] = group.UpdateUser{
			UserID:  userID,
			GroupID: item.ID,
			Active:  item.Active,
		}
	}
	apperr.Error(h.GroupService.UpdateUsers(ctx, uid, wid, sreq))
	ctx.Status(http.StatusOK)
}

type UpdateCompanyInfoReq struct {
	Name string `json:"name"`
	Size string `json:"size"`
}

// UpdateCompanyInfo updates company info
// @Summary update company info
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param UpdateCompanyInfoReq body UpdateCompanyInfoReq true "body"
// @Success 200
// @Router /users/company_info [put]
func (h *Handler) UpdateCompanyInfo(ctx *gin.Context) {
	var req UpdateCompanyInfoReq
	apperr.Error(ctx.BindJSON(&req))
	if err := h.apiValidator.Validate("update_company_info", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}
	uid := ctx.Value("uid").(int)
	apperr.Error(h.Service.UpdateCompanyInfo(ctx, uid, req.Name, req.Size))
	ctx.Status(http.StatusOK)
}

type UserTokenRsp struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description *string   `json:"description,omitempty"`
	ExpireAt    time.Time `json:"expire_at"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type UserTokenListRsp struct {
	Data []UserTokenRsp `json:"data"`
}

// ListUserTokens returns a list of user tokens
// @Summary List user tokens
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param user_id path int true "user_id"
// @Success 200 {object} UserTokenListRsp
// @Router /users/{user_id}/tokens [get]
func (h *Handler) ListUserTokens(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	userID, _ := strconv.Atoi(ctx.Param("user_id"))

	if userID != uid {
		panic(apperr.New(apperr.ErrForbidden, "access denied"))
	}

	tokens, err := h.Service.ListUserTokens(ctx, uid)
	apperr.Error(err)

	rsp := make([]UserTokenRsp, len(tokens))
	for i, token := range tokens {
		rsp[i] = UserTokenRsp{
			ID:          token.ID,
			Name:        token.Name,
			Description: token.Description,
			ExpireAt:    token.ExpireAt,
			Status:      token.Status,
			CreatedAt:   token.CreatedAt,
			UpdatedAt:   token.UpdatedAt,
		}
	}

	ctx.JSON(http.StatusOK, UserTokenListRsp{Data: rsp})
}

type UserTokenCreateReq struct {
	Name        string    `json:"name"`
	Description *string   `json:"description,omitempty"`
	ExpireAt    time.Time `json:"expire_at"`
}

// CreateUserToken creates a new user token
// @Summary Create user token
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param user_id path int true "user_id"
// @Param Req body UserTokenCreateReq true "body"
// @Success 201 {object} UserTokenRsp
// @Router /users/{user_id}/tokens [post]
func (h *Handler) CreateUserToken(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	userID, _ := strconv.Atoi(ctx.Param("user_id"))

	if userID != uid {
		panic(apperr.New(apperr.ErrForbidden, "access denied"))
	}

	var req UserTokenCreateReq
	apperr.ErrorWithCode(apperr.ErrBadRequest, ctx.BindJSON(&req))
	if err := h.apiValidator.Validate("create_user_token", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}

	if req.ExpireAt.Before(time.Now()) {
		panic(apperr.New(apperr.ErrBadRequest, "expire_at must be in the future"))
	}

	token, err := h.Service.CreateUserToken(ctx, uid, user.UserTokenCreateReq(req))
	apperr.Error(err)

	rsp := UserTokenRsp{
		ID:          token.ID,
		Name:        token.Name,
		Description: token.Description,
		ExpireAt:    token.ExpireAt,
		Status:      token.Status,
		CreatedAt:   token.CreatedAt,
		UpdatedAt:   token.UpdatedAt,
	}

	ctx.JSON(http.StatusCreated, rsp)
}

type UpdateUserTokenReq struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	Status      *string `json:"status,omitempty"`
}

// UpdateUserToken updates a user token
// @Summary Update user token
// @Tags Users
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param user_id path int true "user_id"
// @Param token_id path string true "token_id"
// @Param Req body UpdateUserTokenReq true "body"
// @Success 200 {object} UserTokenRsp
// @Router /users/{user_id}/tokens/{token_id} [put]
func (h *Handler) UpdateUserToken(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	userID, _ := strconv.Atoi(ctx.Param("user_id"))
	tokenID := ctx.Param("token_id")

	if userID != uid {
		panic(apperr.New(apperr.ErrForbidden, "access denied"))
	}

	var req UpdateUserTokenReq
	apperr.ErrorWithCode(apperr.ErrBadRequest, ctx.BindJSON(&req))
	if err := h.apiValidator.Validate("update_user_token", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}

	token, err := h.Service.UpdateUserToken(ctx, uid, tokenID, user.UserTokenUpdateReq(req))
	apperr.Error(err)

	rsp := UserTokenRsp{
		ID:          token.ID,
		Name:        token.Name,
		Description: token.Description,
		ExpireAt:    token.ExpireAt,
		Status:      token.Status,
		CreatedAt:   token.CreatedAt,
		UpdatedAt:   token.UpdatedAt,
	}

	ctx.JSON(http.StatusOK, rsp)
}
