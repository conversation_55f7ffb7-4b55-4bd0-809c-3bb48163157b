package workspace

import (
	"context"
	_ "embed"
	"fmt"
	"net/http"
	"sa/internal/license"
	"sa/internal/organization"
	"sa/pkg/httputil"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"sa/apperr"
	"sa/config"
	"sa/internal/authz"
	"sa/internal/invite"
	"sa/internal/user"
	"sa/internal/workspace"
	"sa/pkg/jsonschema"
	"sa/pkg/logging"
)

//go:embed schema_public_create.json
var schemaCreate string

//go:embed schema_public_update.json
var schemaUpdate string

type Handler struct {
	Logger         logging.Logger
	Service        *workspace.Service
	UserService    *user.Service
	LicenseService *license.Service
	InviteService  invite.Service
	OS             *organization.Service
	Cfg            *config.Config
	apiValidator   jsonschema.APIValidator
}

func (h *Handler) PublicRegister(rg *gin.RouterGroup) {
	rg.GET("", h.Workspace)
	rg.PUT("", h.Update)
	rg.DELETE("", h.Delete)
}

func (h *Handler) RegisterInWorkspace(rg *gin.RouterGroup) {
	rg.GET("", h.Workspace)
	rg.PUT("", h.Update)
	rg.DELETE("", h.Delete)
}

func (h *Handler) Register(rg *gin.RouterGroup) {
	rg.GET("", h.Workspaces)
	rg.POST("", h.Create)

	h.apiValidator = jsonschema.NewAPIValidator()
	h.apiValidator.Add("create", schemaCreate)
	h.apiValidator.Add("update", schemaUpdate)
}

// Workspaces lists all workspaces
// @Summary List all workspaces
// @Tags Workspaces
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param   sa-user-id       header  string    false  "SA userID"
// @Param search query string false "search query"
// @Param org_id query string false "organization id (value `me` for current user)"
// @Param limit query int false "limit for lazy loading"
// @Param offset query int false "offset for lazy loading"
// @Param sort_by query string false "sort by field, available values: name"
// @Param order_by query string false "order by asc or desc"
// @Success 200 {object} ShortRsp
// @Router /workspaces [get]
func (h *Handler) Workspaces(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	targetUserIDBin := ctx.GetHeader("sa-user-id")
	if targetUserIDBin != "" {
		tUID, _ := strconv.Atoi(targetUserIDBin)
		if tUID != uid {
			if err := h.Service.AS.MustHave(ctx, uid, 0, authz.TypePermEnv, authz.PermEnvWorkspacesView); err != nil {
				panic(apperr.New(apperr.ErrForbidden, "not allowed"))
			}
		}
		uid = tUID
	}

	rsp := h.userWorkspaces(ctx, uid)
	ctx.JSON(http.StatusOK, rsp)
}

func (h *Handler) userWorkspaces(ctx *gin.Context, uid int) ShortRsp {
	oidBin := ctx.Query("org_id")
	memberIDs := make([]int, 0)
	roleID := 0
	if oidBin != "" {
		if oidBin != "me" {
			panic(apperr.New(apperr.ErrBadRequest, "org_id is invalid"))
		}
		oid := h.OS.OrganizationIDByExtID(ctx, "user-"+strconv.Itoa(uid))
		dmns := h.OS.GetDomains(ctx, oid)
		for _, dm := range dmns {
			memberIDs = append(memberIDs, h.UserService.GetIDsByDomain(ctx, dm.Domain)...)
		}
		roleID = authz.RoleOwner
		memberIDs = append(memberIDs, uid)
	}
	search, limit, offset, sortBy, orderBy := httputil.GetListQueries(ctx, "name")

	wss, count := h.Service.List(ctx,
		workspace.Filter{
			RoleID:    roleID,
			MemberIDs: memberIDs,
			Search:    search, IDs: []int{}, UID: uid},
		limit, offset, sortBy, orderBy)

	ids := make([]int, len(wss))
	for i, item := range wss {
		ids[i] = item.ID
	}
	owners := h.Service.ListOwners(ctx, ids)
	roles := h.Service.ListRoles(ctx, ids, uid)
	rsp := make([]RspItem, 0)
	for _, item := range wss {
		itemOwners := make([]OwnerRow, 0)
		for _, oo := range owners[item.ID] {
			itemOwners = append(itemOwners, OwnerRow{
				ID:    oo.ID,
				Name:  oo.Name,
				Photo: oo.Photo,
				Color: oo.Color,
			})
		}
		itemRoles := make([]RoleRow, 0)
		for _, oo := range roles[item.ID] {
			itemRoles = append(itemRoles, RoleRow{
				ID:   oo.ID,
				Name: oo.Name,
			})
		}
		status := "active"
		if item.Status == 1 {
			status = "blocked"
		}
		userStatus := "active"
		if item.UserBlocked {
			userStatus = "blocked"
		}
		rsp = append(rsp, RspItem{
			ID:         item.ID,
			ExtID:      item.ExtID,
			Name:       item.Name,
			Photo:      item.Photo,
			Color:      item.Color,
			Status:     status,
			CreatedAt:  item.CreatedAt,
			Owners:     itemOwners,
			UserStatus: userStatus,
			Roles:      itemRoles,
		})
	}
	return ShortRsp{
		Meta: RspMeta{
			Total: count,
		},
		Data: rsp,
	}
}

// Workspace gives all workspace details
// @Summary Get workspace details
// @Tags Workspaces
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Success 200 {object} Rsp
// @Router /workspaces/{workspace_id} [get]
func (h *Handler) Workspace(ctx *gin.Context) {

	uid := ctx.Value("uid").(int)
	wid := ctx.Value("wid").(int)
	if err := h.Service.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermWorkspace); err != nil {
		panic(fmt.Errorf("no perms: %w", err))
	}
	rsp := h.getWorkspace(ctx, wid, uid)
	ctx.JSON(http.StatusOK, rsp)
}

// Create creates workspace
// @Summary Create workspace
// @Tags Workspaces
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param Req body Req true "body"
// @Success 201 {object} Rsp
// @Router /workspaces [post]
func (h *Handler) Create(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	var req Req
	err := ctx.BindJSON(&req)
	apperr.Error(err)
	req.Name = strings.TrimSpace(req.Name)
	if err := h.apiValidator.Validate("create", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}
	sLogins, err := h.UserService.GetLogins(ctx, []int{uid})
	apperr.Error(err)
	logins := make([]string, len(sLogins))
	for i, item := range sLogins {
		logins[i] = item.Login
	}
	if h.OS.IsForbiddenToCreateWS(ctx, uid, logins) {
		panic(apperr.New(apperr.ErrForbidden, "not allowed by identity provider"))
	}
	wreq := workspace.CreateRow{
		Name:  req.Name,
		Photo: req.Photo,
		Color: req.Color,
		Users: []workspace.CreateRowUsers{{
			UserID: uid,
			RoleID: authz.RoleOwner,
		}},
	}
	id, _, err := h.Service.Create(ctx, wreq)
	apperr.Error(err)
	rsp := h.getWorkspace(ctx, id, uid)
	ctx.JSON(http.StatusCreated, rsp)
}

// Update workspace (name or/and status)
// @Summary Update workspace (name or/and status)
// @Tags Workspaces
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Param ReqUpdate body ReqUpdate true "body"
// @Success 200 {object} Rsp
// @Router /workspaces/{workspace_id} [put]
func (h *Handler) Update(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	wid := ctx.Value("wid").(int)

	var req ReqUpdate
	err := ctx.BindJSON(&req)
	apperr.Error(err)
	req.Name = strings.TrimSpace(req.Name)
	if err := h.apiValidator.Validate("update", req); err != nil {
		panic(apperr.New(apperr.ErrBadRequest, err.Error()))
	}
	if req.Settings.AllowedDomains != nil && req.Settings.ForbiddenDomainsRe != nil &&
		len(*req.Settings.AllowedDomains) > 0 && len(*req.Settings.ForbiddenDomainsRe) > 0 {
		panic(apperr.New(apperr.ErrBadRequest, "allowed_domains and forbidden_domains can't be set at the same time"))
	}
	if req.Settings.FileTTL != nil && *req.Settings.FileTTL < 0 {
		panic(apperr.New(apperr.ErrBadRequest, "file_ttl can't be negative"))
	}
	aiSettings := make([]workspace.AISetting, len(req.Settings.AISettings))
	for i, ai := range req.Settings.AISettings {
		aiSettings[i] = workspace.AISetting{
			Provider: ai.Provider,
			APIKey:   ai.APIKey,
		}
	}
	wreq := workspace.UpdateRow{
		Name:  req.Name,
		Photo: req.Photo,
		Color: req.Color,
		Settings: workspace.Settings{
			DisableInvites:    req.Settings.DisableInvites,
			AllowedDomains:    req.Settings.AllowedDomains,
			ForbiddenDomains:  req.Settings.ForbiddenDomainsRe,
			SimClient:         req.Settings.SimClient,
			AutoRecording:     req.Settings.AutoRecording,
			TranscriptionLang: req.Settings.TranscriptionLang,
			FileTTL:           req.Settings.FileTTL,
			AISettings:        aiSettings,
		},
	}

	err = h.Service.Update(ctx, uid, wid, wreq)
	if err != nil {
		panic(err)
	}
	if req.Status == "block" || req.Status == "blocked" {
		panic(apperr.New(apperr.ErrForbidden, "Temporary you can't block workspace"))
	} else {
		err = h.Service.UnBlock(ctx, uid, wid)
		apperr.Error(err)
	}
	rsp := h.getWorkspace(ctx, wid, uid)
	ctx.JSON(http.StatusOK, rsp)
}

// Delete deletes workspace
// @Summary Delete workspace
// @Tags Workspaces
// @Security BasicAuth
// @Accept json
// @Produce json
// @Param workspace_id path string true "workspace id"
// @Success 200
// @Router /workspaces/{workspace_id} [delete]
func (h *Handler) Delete(ctx *gin.Context) {
	uid := ctx.Value("uid").(int)
	wid := ctx.Value("wid").(int)
	err := h.Service.Delete(ctx, uid, wid)
	apperr.Error(err)
	ctx.Status(http.StatusOK)
}

func (h *Handler) getWorkspace(ctx context.Context, workspaceID, uid int) Rsp {
	owners := h.Service.ListOwners(ctx, []int{workspaceID})
	wUID := uid
	if err := h.Service.AS.MustHave(ctx, uid, 0, authz.TypePermEnv, authz.PermEnvWorkspacesView); err == nil {
		wUID = owners[workspaceID][0].ID
	}
	ws, err := h.Service.Get(ctx, workspaceID, wUID)
	apperr.Error(err)

	itemOwners := make([]OwnerRow, 0)
	for _, oo := range owners[ws.ID] {
		itemOwners = append(itemOwners, OwnerRow{
			ID:    oo.ID,
			Name:  oo.Name,
			Photo: oo.Photo,
			Color: oo.Color,
		})
	}
	status := "active"
	if ws.Status == 1 {
		status = "blocked"
	}
	wType := "regular"
	if h.Cfg.AdminWorkspace == ws.ExtID {
		wType = "null"
	}
	aiSettings := make([]AISetting, len(ws.Settings.AISettings))
	for i, ai := range ws.Settings.AISettings {
		aiSettings[i] = AISetting{
			Provider: ai.Provider,
			APIKey:   ai.APIKey,
		}
	}
	rsp := Rsp{
		ID:          ws.ID,
		ExtID:       ws.ExtID,
		Name:        ws.Name,
		Photo:       ws.Photo,
		Color:       ws.Color,
		Status:      status,
		CreatedAt:   ws.CreatedAt,
		UserCount:   ws.UsersCount,
		GroupCount:  ws.GroupsCount,
		RoleCount:   ws.RolesCount,
		Owners:      itemOwners,
		InviteCount: ws.InvitesCount,
		APICount:    ws.APIUsersCount,
		Type:        wType,
		Settings: Settings{
			DisableInvites:     ws.Settings.DisableInvites,
			AllowedDomains:     ws.Settings.AllowedDomains,
			ForbiddenDomainsRe: ws.Settings.ForbiddenDomains,
			SimClient:          ws.Settings.SimClient,
			AutoRecording:      ws.Settings.AutoRecording,
			TranscriptionLang:  ws.Settings.TranscriptionLang,
			FileTTL:            ws.Settings.FileTTL,
			AISettings:         aiSettings,
		},
	}

	return rsp
}
