{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://schema.sa.com/schema_create.json", "type": "object", "required": ["name", "status"], "properties": {"photo": {"type": "string", "minLength": 1, "maxLength": 500, "pattern": "https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,20}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)"}, "color": {"type": "string", "minLength": 7, "maxLength": 7, "pattern": "^#[A-Fa-f0-9]{6}$"}, "name": {"type": "string", "minLength": 1, "maxLength": 255, "pattern": "^[\\p{L}\\+\\-.,@!'ʼ’\"`0-9_ ]+$"}, "status": {"type": "string", "enum": ["active", "block", "blocked"]}, "settings": {"type": "object", "properties": {"disable_invites": {"type": "boolean"}, "allowed_subdomains": {"type": "array", "items": {"pattern": "^[a-zA-Z0-9-]+\\.([a-zA-Z]{2,}\\.[a-zA-Z]{2,}|[a-zA-Z]{2,})$", "type": "string", "minLength": 1, "maxLength": 50}}, "forbidden_domains": {"type": "array", "items": {"pattern": "^[a-zA-Z0-9-]+\\.([a-zA-Z]{2,}\\.[a-zA-Z]{2,}|[a-zA-Z]{2,})$", "type": "string", "minLength": 1, "maxLength": 50}}, "auto_recording": {"type": "boolean"}, "file_ttl": {"type": "integer", "minimum": 3, "maximum": 365}, "auto_transcription": {"type": "boolean"}, "transcription_lang": {"type": "string", "enum": ["en", "es", "da", "de", "fr", "hi", "id", "it", "ja", "ko", "nl", "no", "pl", "pt", "ru", "sv", "ta", "taq", "th", "tr", "uk", "zh", "zh-CN", "zh-TW"]}}}}}