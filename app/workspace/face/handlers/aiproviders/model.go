package aiproviders

type OpenAIModel string

const (
	OpenAIGPT4       OpenAIModel = "gpt-4"
	OpenAIGPT4Turbo  OpenAIModel = "gpt-4-turbo"
	OpenAIGPT41      OpenAIModel = "gpt-4.1"
	OpenAIGPT41Mini  OpenAIModel = "gpt-4.1-mini"
	OpenAIGPT41Nano  OpenAIModel = "gpt-4.1-nano"
	OpenAIGPT4O      OpenAIModel = "gpt-4o"
	OpenAIGPT45      OpenAIModel = "gpt-4.5"
	OpenAIGPT35Turbo OpenAIModel = "gpt-3.5-turbo"
	OpenAIO1         OpenAIModel = "o1"
	OpenAIO1Mini     OpenAIModel = "o1-mini"
	OpenAIO1Pro      OpenAIModel = "o1-pro"
	OpenAIO3         OpenAIModel = "o3"
	OpenAIO3Mini     OpenAIModel = "o3-mini"
	OpenAIO4Mini     OpenAIModel = "o4-mini"
)

type AzureOpenAIModelID string

const (
	AzureOpenAIGPT41      AzureOpenAIModelID = "gpt-4.1"
	AzureOpenAIGPT41Mini  AzureOpenAIModelID = "gpt-4.1-mini"
	AzureOpenAIGPT41Nano  AzureOpenAIModelID = "gpt-4.1-nano"
	AzureOpenAIGPT35Turbo AzureOpenAIModelID = "gpt-3.5-turbo"
)

type AzureOpenAIDeployment struct {
	DeploymentName string             `json:"deploymentName"`
	ModelID        AzureOpenAIModelID `json:"modelId"`
}

type ClaudeModel string

const (
	Claude3Haiku   ClaudeModel = "claude-3-haiku"
	Claude3Sonnet  ClaudeModel = "claude-3-sonnet"
	Claude3Opus    ClaudeModel = "claude-3-opus"
	Claude35Haiku  ClaudeModel = "claude-3.5-haiku"
	Claude35Sonnet ClaudeModel = "claude-3.5-sonnet"
	Claude37Sonnet ClaudeModel = "claude-3.7-sonnet"
	Claude4Opus    ClaudeModel = "claude-4-opus"
	Claude4Sonnet  ClaudeModel = "claude-4-sonnet"
)

type DeepSeekModel string

const (
	DeepSeekR1      DeepSeekModel = "deepseek-r1"
	DeepSeekV2      DeepSeekModel = "deepseek-v2"
	DeepSeekV3      DeepSeekModel = "deepseek-v3"
	DeepSeekCoder   DeepSeekModel = "deepseek-coder"
	DeepSeekCoderV2 DeepSeekModel = "deepseek-coder-v2"
	DeepSeekMath    DeepSeekModel = "deepseek-math"
)

type RspAIProviderItem struct {
	Vendor     string                 `json:"vendor"`
	Model      interface{}            `json:"model,omitempty"`
	Deployment *AzureOpenAIDeployment `json:"deployment,omitempty"`
}

type RspAIProviders []RspAIProviderItem
