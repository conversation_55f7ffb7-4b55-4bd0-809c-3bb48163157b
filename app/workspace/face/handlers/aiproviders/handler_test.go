package aiproviders_test

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"sa/app/workspace/face/handlers/aiproviders"
	"sa/testhelper"
)

func TestHandler_AIProviders(t *testing.T) {
	ss := testhelper.NewPack()

	h := aiproviders.Handler{
		Logger: ss.L,
	}
	h.Register(ss.EngineFace.Group(""))

	uid, _ := ss.CreateRegularUser(ss.Ctx)

	w := httptest.NewRecorder()
	req, _ := http.NewRequestWithContext(ss.Ctx, http.MethodGet, "/", http.NoBody)
	req.Header.Set("sa-user-id", strconv.Itoa(uid))
	ss.EngineFace.ServeHTTP(w, req)

	require.Equal(t, http.StatusOK, w.Code)

	var rsp aiproviders.RspAIProviders
	err := json.Unmarshal(w.Body.Bytes(), &rsp)
	require.NoError(t, err)

	assert.NotEmpty(t, rsp)

	openAICount := 0
	azureCount := 0
	claudeCount := 0
	deepSeekCount := 0

	for _, provider := range rsp {
		switch provider.Vendor {
		case "openai":
			openAICount++
			assert.NotNil(t, provider.Model)
			assert.Nil(t, provider.Deployment)
		case "azure-openai":
			azureCount++
			assert.Nil(t, provider.Model)
			assert.NotNil(t, provider.Deployment)
			assert.NotEmpty(t, provider.Deployment.DeploymentName)
			assert.NotEmpty(t, provider.Deployment.ModelID)
		case "claude":
			claudeCount++
			assert.NotNil(t, provider.Model)
			assert.Nil(t, provider.Deployment)
		case "deepseek":
			deepSeekCount++
			assert.NotNil(t, provider.Model)
			assert.Nil(t, provider.Deployment)
		}
	}

	assert.Equal(t, 14, openAICount)
	assert.Equal(t, 4, azureCount)
	assert.Equal(t, 8, claudeCount)
	assert.Equal(t, 6, deepSeekCount)
}
