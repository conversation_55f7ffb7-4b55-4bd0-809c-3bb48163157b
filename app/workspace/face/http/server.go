package http

import (
	"context"
	"encoding/json"
	"errors"
	"log/slog"
	"net/http"
	horganization "sa/app/workspace/face/handlers/organization"
	"sa/app/workspace/face/handlers/ownerrequest"
	hwebhook "sa/app/workspace/face/handlers/webhook"
	"sa/internal/oauth2"
	"sa/internal/webhook"
	"sa/pkg/httputil"
	"sa/pkg/pgsql"
	"sa/pkg/prom"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"

	haiproviders "sa/app/workspace/face/handlers/aiproviders"
	hlicense "sa/app/workspace/face/handlers/license"
	"sa/app/workspace/face/handlers/sa"
	"sa/internal/authz"
	"sa/internal/license"

	hapi "sa/app/workspace/face/handlers/api"
	hclient "sa/app/workspace/face/handlers/client"
	hgroup "sa/app/workspace/face/handlers/group"
	hinvite "sa/app/workspace/face/handlers/invite"
	hperm "sa/app/workspace/face/handlers/perm"
	hrole "sa/app/workspace/face/handlers/role"
	hscope "sa/app/workspace/face/handlers/scope"
	huser "sa/app/workspace/face/handlers/user"
	hworkspace "sa/app/workspace/face/handlers/workspace"
	"sa/apperr"
	"sa/config"
	"sa/internal/base"
	"sa/internal/client"
	"sa/internal/group"
	"sa/internal/invite"
	"sa/internal/organization"
	"sa/internal/perm"
	"sa/internal/role"
	"sa/internal/scope"
	"sa/internal/user"
	"sa/internal/workspace"
	"sa/pkg/logging"
	"sa/pkg/rand"
)

func MarshalError(ctx *gin.Context, err error) []byte {
	var rsp map[string]string
	msg := apperr.Msgs[apperr.ErrInternalError]
	var ae *apperr.AppError
	var ce *apperr.CodeError
	switch {
	case errors.As(err, &ae):
		ref := ae.Ref
		if m, ok := apperr.Msgs[ae.Code]; ok {
			msg = m
			if ae.Code != apperr.ErrInternalError {
				ref = ""
			}
		}
		rsp = map[string]string{
			"code": ae.Code.Error(),
			"msg":  msg,
			"ref":  ref,
		}
	case errors.As(err, &ce):
		ref := rand.Seq(10)
		if m, ok := apperr.Msgs[ce]; ok {
			msg = m
			if ce != apperr.ErrInternalError {
				ref = ""
			}
		}
		rsp = map[string]string{
			"code": ce.Error(),
			"msg":  msg,
			"ref":  ref,
		}
	default:
		rsp = map[string]string{
			"code": apperr.ErrInternalError.Error(),
			"msg":  msg,
			"ref":  rand.Seq(10),
		}
	}

	rspBin, err := json.Marshal(rsp)
	apperr.Log(err, "json.Marshal")
	ctx.Set("rsp", rsp)
	return rspBin
}

type VersionRsp struct {
	Tag string `json:"tag"`
}
type VersionHandler struct {
	Cfg *config.Config
}

// Version gets version of API
// @Summary Get version of API
// @Tags Version
// @Accept json
// @Produce json
// @Success 200 {object} VersionRsp
// @Router /version [get]
func (h *VersionHandler) Version(ctx *gin.Context) {
	slog.Info("Version called")
	ctx.JSON(http.StatusOK, VersionRsp{Tag: h.Cfg.Tag})
}

func AuthFaceMiddleware(us *user.Service, cfg *config.Config, opt oauth2.Opt) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		err := oauth2.Authorize(ctx, us, cfg, opt)
		httpCode := http.StatusUnauthorized
		errrCode := apperr.ErrUnauthorized
		if errors.Is(err, apperr.ErrRequireMFA) {
			errrCode = apperr.ErrRequireMFA
		}
		if err != nil {
			slog.Error("", "err", err)
			ctx.Data(httpCode, "application/json", MarshalError(ctx, errrCode))
			ctx.Abort()
			return
		}
	}
}

func SetupRouter(ctx context.Context, router *gin.RouterGroup, bs *base.Base, l logging.Logger, conn *sqlx.DB, authFunc func(us *user.Service, cfg *config.Config, opt oauth2.Opt) gin.HandlerFunc) {

	router.Use(httputil.JSONLogMiddleware(ctx, logging.TypeFace, "/face", "uid")) //nolint:contextcheck // no need to pass context
	router.Use(apperr.Middleware(MarshalError))

	router.Use(pgsql.WithDBMiddleware(ctx, conn)) //nolint:contextcheck // no need to pass context
	routerAPI := router.Group("/face/api/1")
	routerAPI.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		prom.InboundRequests.WithLabelValues("face", c.FullPath(), c.Request.Method, strconv.Itoa(c.Writer.Status())).
			Observe(float64(time.Since(start).Milliseconds()))
	})

	ps := perm.NewService(bs)
	ss := scope.NewService(bs)
	rs := role.NewService(bs)
	ls := license.NewService(bs)
	us := user.NewService(ls, rs, ss, bs)

	routerAuth := routerAPI.Group("")
	var routerAuthOpt oauth2.Opt
	if bs.Cfg.SingleAccountDisabled {
		routerAuthOpt = oauth2.OptNoSingleAccount
	}
	routerAuth.Use(authFunc(us, bs.Cfg, routerAuthOpt))

	routerAuthLicense := routerAuth.Group("")

	is := invite.NewService(rs, us, bs, invite.NewStorage())
	gs := group.NewService(us, bs, group.NewStorage())
	ws := workspace.NewService(gs, us, rs, bs, workspace.NewStorage())

	cs := client.NewService(bs)
	os := organization.NewService(bs)

	whs := webhook.NewService()

	routerAuthLicense.Use(func(ctx *gin.Context) {
		if valid, err := bs.License.IsValid(); !valid {
			panic(err)
		}
		ui := ctx.Value("ui").(user.Row)
		if ui.Status != authz.StatusUserActive {
			panic(apperr.New(apperr.ErrForbidden, "user blocked"))
		}
	})
	wHandler := hworkspace.Handler{
		UserService:    us,
		InviteService:  is,
		LicenseService: ls,
		OS:             os,

		Service: ws, Logger: l, Cfg: bs.Cfg,
	}
	rHandler := hrole.Handler{Service: rs, AuthzService: bs.AS, Logger: l, US: us}
	gHandler := hgroup.Handler{UserService: us, Service: gs, RoleService: rs, Logger: l}
	uHandler := huser.NewHandler(ws, us, rs, gs, is)
	aHandler := hapi.Handler{Service: us, Logger: l, Cfg: bs.Cfg}
	iHandler := hinvite.Handler{Service: is, Logger: l, Cfg: bs.Cfg, AuthzService: bs.AS}
	pHandler := hperm.Handler{PS: ps, Logger: l, Cfg: bs.Cfg}
	sHandler := hscope.Handler{SS: ss, Logger: l}
	cHandler := hclient.Handler{Service: cs, Logger: l}
	lHandler := hlicense.Handler{RS: rs, WS: ws, US: us, LS: ls, Logger: l}
	saHandler := sa.Handler{BS: bs}
	whHandler := hwebhook.Handler{Service: whs, US: us}
	orHandler := ownerrequest.Handler{RS: rs, US: us, WS: ws, GS: gs}
	oHandler := horganization.NewHandler(os, ls, ws, us, rs)
	aiHandler := haiproviders.Handler{Logger: l}

	wGroup := routerAuthLicense.Group("/workspaces")
	wHandler.Register(wGroup)
	wItemGroup := wGroup.Group("/:workspace_id")
	wItemGroup.Use(func(ctx *gin.Context) { //nolint:contextcheck // there is context
		uid := ctx.Value("uid").(int)
		wid, err := strconv.Atoi(ctx.Param("workspace_id"))
		if err != nil {
			wExtID := ctx.Param("workspace_id")
			wid, err = ws.GetByExtID(ctx, wExtID)
			if err != nil {
				panic(apperr.New(apperr.ErrWorkspaceNotFound))
			}
		}
		if len(rs.CheckUsers(ctx, wid, []int{uid})) == 0 {
			if bs.AS.MustHave(ctx, uid, 0, authz.TypePermEnv, authz.PermEnvWorkspacesView, authz.PermEnvWorkspacesManagement) != nil {
				if !bs.AS.SameOrganization(ctx, uid, wid) {
					panic(apperr.New(apperr.ErrWorkspaceNotFound))
				}
			}
			ctx.Set("wid", wid)
			ctx.Set("lang", "en")
			return
		}
		ui, _ := us.Get(ctx, 0, wid, uid)

		if ui.RuleStatus != nil {
			if bs.AS.MustHave(ctx, uid, 0, authz.TypePermEnv, authz.PermEnvWorkspacesView, authz.PermEnvWorkspacesManagement) != nil {
				panic(apperr.New(apperr.ErrNoPermissions, "user is blocked"))
			}
		}

		ctx.Set("wid", wid)
		ctx.Set("lang", "en")
	})
	wHandler.RegisterInWorkspace(wItemGroup)
	rHandler.Register(wItemGroup.Group("/roles"))
	wHandler.RegisterAdmin(routerAuth.Group("/admin/workspaces"))
	gHandler.Register(wItemGroup.Group("/groups"))
	uHandler.Register(routerAuth.Group("/users"))
	uHandler.RegisterAdmin(routerAuth.Group("/admin/users"))
	uHandler.RegisterInWorkspace(wItemGroup.Group("/users"))
	aHandler.Register(wItemGroup.Group("/apis"))
	iHandler.Register(wItemGroup.Group("/invites"))
	pHandler.Register(routerAuthLicense.Group("/perms"))
	sHandler.Register(routerAuthLicense.Group("/scopes"))
	cHandler.Register(routerAPI.Group("/clients"))
	lHandler.RegisterPublic(routerAPI.Group("/licenses"))
	lHandler.Register(routerAuthLicense.Group("/licenses"))
	lHandler.RegisterAdmin(routerAuth.Group("/admin/licenses"))
	saHandler.Register(routerAPI.Group("/auth"))
	whHandler.Register(ctx, wItemGroup.Group("/webhooks"))
	orHandler.Register(routerAuthLicense.Group("/owner_request"))
	oHandler.Register(ctx, routerAuthLicense.Group("/organizations"))
	aiHandler.Register(routerAPI.Group("/ai_providers"))

}
