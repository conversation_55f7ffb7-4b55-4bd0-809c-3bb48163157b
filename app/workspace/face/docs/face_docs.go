// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplateface = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/licenses": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Create new custom license",
                "parameters": [
                    {
                        "description": "body",
                        "name": "CreateCustomReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.CreateCustomReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/admin/licenses/import": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Import licenses from file [DEPRECATED]",
                "operationId": "file.upload",
                "parameters": [
                    {
                        "type": "file",
                        "description": "this is a test file",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/licenses/payments": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Create payment",
                "parameters": [
                    {
                        "description": "body",
                        "name": "CreatePaymentReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.CreatePaymentReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/license.CreatePaymentRsp"
                        }
                    }
                }
            }
        },
        "/admin/licenses/payments/{payment_id}/approve": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Approve payment",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "payment id",
                        "name": "payment_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "ApprovePaymentReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.ApprovePaymentReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/admin/licenses/payments/{payment_id}/reject": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Reject payment",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "payment id",
                        "name": "payment_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "RejectPaymentReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.RejectPaymentReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/admin/licenses/root": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Get current root license",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.AdminRootLicenseRsp"
                        }
                    }
                }
            }
        },
        "/admin/licenses/root/upload": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Upload license file",
                "parameters": [
                    {
                        "description": "body",
                        "name": "Upload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.Upload"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.AdminRootLicenseDiffRsp"
                        }
                    }
                }
            }
        },
        "/admin/licenses/root/{ref}/apply": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "tags": [
                    "License management"
                ],
                "summary": "apply new license by ref",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ref",
                        "name": "ref",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/licenses/root/{ref}/diff": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Diff between the new license and the current one",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ref",
                        "name": "ref",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.AdminRootLicenseDiffRsp"
                        }
                    }
                }
            }
        },
        "/admin/licenses/sharing": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "License Sharing: List all links",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "parent user id",
                        "name": "parent_user_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "child user id",
                        "name": "child_user_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/license.LSRow"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "License Sharing: Share license with user",
                "parameters": [
                    {
                        "description": "body",
                        "name": "CreateLSReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.CreateLSReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/licenses/sharing/{child_user_id}": {
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "License Sharing: Delete sharing",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "child user id",
                        "name": "child_user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/licenses/used_state_changes": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Provide used state_changes for the period",
                "parameters": [
                    {
                        "type": "string",
                        "description": "license id",
                        "name": "license_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "user_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "from 2006-12-01",
                        "name": "from",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "to 2006-12-31",
                        "name": "to",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.UsageStateChanges"
                        }
                    }
                }
            }
        },
        "/admin/licenses/users/{user_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Get license by user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RspDetailed"
                        }
                    }
                }
            }
        },
        "/admin/licenses/workspaces/{workspace_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Get license by workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RspDetailed"
                        }
                    }
                }
            }
        },
        "/admin/licenses/{license_id}/attrs/add": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Add license attr",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "AddAttrReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.AddAttrReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/licenses/{license_id}/attrs/update": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Update license attr",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateAttrReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.UpdateAttrReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/licenses/{license_id}/cancel": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Cancel license",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/licenses/{license_id}/dates": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Update license dates",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/users": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User management"
                ],
                "summary": "List all users in the environment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "filter by status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name, workspace_count, last_entrance, created_at",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.AdminRsp"
                        }
                    }
                }
            }
        },
        "/admin/users/expire_password": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User management"
                ],
                "summary": "Make the password expired",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user login",
                        "name": "login",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "userID",
                        "name": "user-id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/users/logout": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User management"
                ],
                "summary": "Logout specified user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user login",
                        "name": "login",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "userID",
                        "name": "user-id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/users/reset_password": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User management"
                ],
                "summary": "Reset password for specified user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user login",
                        "name": "login",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "userID",
                        "name": "user-id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/users/{user_id}/2fa": {
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User management"
                ],
                "summary": "delete 2FA for user",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/users/{user_id}/status": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User management"
                ],
                "summary": "Update user status",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UpdateStatusReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/admin/workspaces": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspace management"
                ],
                "summary": "List all workspaces",
                "parameters": [
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by statuses: active, blocked",
                        "name": "statuses",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/workspace.AdminRsp"
                        }
                    }
                }
            }
        },
        "/api_key": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "API key (create API key, update API Key, block API Key, unblock API Key, Remove API Key)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleAPIKey",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleAPIKey"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/clients": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Clients"
                ],
                "summary": "List all clients",
                "parameters": [
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/client.Row"
                            }
                        }
                    }
                }
            }
        },
        "/group": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "Groups (create group, rename group, delete group, transfer ownership)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleGroup",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleGroup"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/group/user": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "Group users (add user to the group, remove user from group)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleGroupUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleGroupUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/invite": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "Invites (create invite, delete invite)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleInvite",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleInvite"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/licenses": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "List all license",
                "parameters": [
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by statuses",
                        "name": "statuses",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by payment_type (online,invoice,free)",
                        "name": "payment_type",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "return all licenses(default false)",
                        "name": "all",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.Rsp"
                        }
                    }
                }
            }
        },
        "/licenses/create": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Create license",
                "parameters": [
                    {
                        "type": "string",
                        "description": "license type",
                        "name": "type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "license payment_type",
                        "name": "payment_type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "license period 1 or 12",
                        "name": "period",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "number",
                        "description": "license price",
                        "name": "price",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "license cancel_url",
                        "name": "cancel_url",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "license success_url",
                        "name": "success_url",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "license rps",
                        "name": "rps",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "license support",
                        "name": "support",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "license storage",
                        "name": "storage",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "license source",
                        "name": "source",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "license state_changes_per_period_m",
                        "name": "state_changes_per_period_m",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "302": {
                        "description": "Found"
                    }
                }
            }
        },
        "/licenses/payments": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "List payments",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: id, created_at, status",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by statuses",
                        "name": "statuses",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/license.RspPayment"
                            }
                        }
                    }
                }
            }
        },
        "/licenses/payments/{payment_id}/download": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Download invoice",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "payment id",
                        "name": "payment_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/licenses/tariffs": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Get tariffs",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/license.CloudTariffItem"
                            }
                        }
                    }
                }
            }
        },
        "/licenses/user/{user_id}/status": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user status",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.UpdateStatusReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/licenses/users": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "license users list by owner",
                "parameters": [
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: status",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.Users"
                        }
                    }
                }
            }
        },
        "/licenses/{license_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Get license",
                "parameters": [
                    {
                        "type": "string",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RspDetailed"
                        }
                    }
                }
            }
        },
        "/licenses/{license_id}/attributes/buy": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Buy attributes",
                "parameters": [
                    {
                        "type": "string",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "BuyAttrReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.BuyAttrReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/license.PaymentInfo"
                        }
                    }
                }
            }
        },
        "/licenses/{license_id}/attrs/rps": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Update license rps",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateRPSReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.UpdateRPSReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/licenses/{license_id}/cancel": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Cancel license",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RspDetailed"
                        }
                    }
                }
            }
        },
        "/licenses/{license_id}/comment": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Update license comment",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateCommentReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.UpdateCommentReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RspDetailed"
                        }
                    }
                }
            }
        },
        "/licenses/{license_id}/download": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Download license",
                "parameters": [
                    {
                        "type": "string",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "enum": [
                            "binary",
                            "base64"
                        ],
                        "type": "string",
                        "description": "format",
                        "name": "format",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/licenses/{license_id}/stripe/update_payment_method": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Update stripe payment_method",
                "parameters": [
                    {
                        "type": "string",
                        "description": "license id",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.GetStripeLinkRsp"
                        }
                    }
                }
            }
        },
        "/login": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "Logins",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleLogin",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleLogin"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/organizations/env/idp/{idp_id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Update identity provider in the environment",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "identity provider id",
                        "name": "idp_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/organization.EnvIDPSet"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/organization.IDPRsp"
                        }
                    }
                }
            }
        },
        "/organizations/{org_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Get organization details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (support only ` + "`" + `me` + "`" + ` value for current user)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/organization.OrgRsp"
                            }
                        }
                    }
                }
            }
        },
        "/organizations/{org_id}/domains": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "List domains",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id, for default organization use ` + "`" + `user-\u003cuser_id\u003e` + "`" + `",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/organization.DomainRsp"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Add domain to organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id, for default organization use ` + "`" + `user-\u003cuser_id\u003e` + "`" + `",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "domain",
                        "name": "OrgDomainReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/organization.OrgDomainReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/organizations/{org_id}/domains/{domain_id}": {
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Delete domain from organization",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id, for default organization use ` + "`" + `user-\u003cuser_id\u003e` + "`" + `",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "domain",
                        "name": "domain_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/organizations/{org_id}/idp": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "List all identity providers",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (` + "`" + `me` + "`" + ` for current user, 'env' for environment)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/organization.IDPRsp"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Create identity provider",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (value ` + "`" + `me` + "`" + ` for current user)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "boolean",
                        "description": "is_test",
                        "name": "is_test",
                        "in": "query"
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/organization.IDPSet"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/organization.IDPRsp"
                        }
                    }
                }
            }
        },
        "/organizations/{org_id}/idp/{idp_id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Update identity provider",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (value ` + "`" + `me` + "`" + ` for current user)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "identity provider id",
                        "name": "idp_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "boolean",
                        "description": "is_test",
                        "name": "is_test",
                        "in": "query"
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/organization.IDPSet"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/organization.IDPRsp"
                        }
                    }
                }
            }
        },
        "/organizations/{org_id}/idp/{idp_id}/maps": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "List map elements of the identity provider",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (` + "`" + `me` + "`" + ` for current user, 'env' for environment)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "identity provider id",
                        "name": "idp_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/organization.Map"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Create map element of the identity provider",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (` + "`" + `me` + "`" + ` for current user, 'env' for environment)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "identity provider id",
                        "name": "idp_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/organization.CreateUpdateMap"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/organization.Map"
                        }
                    }
                }
            }
        },
        "/organizations/{org_id}/idp/{idp_id}/maps/{map_id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Update map element of the identity provider",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (` + "`" + `me` + "`" + ` for current user, 'env' for environment)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "identity provider id",
                        "name": "idp_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "mapping id",
                        "name": "map_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/organization.CreateUpdateMap"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/organization.Map"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Delete map element of the identity provider.",
                "parameters": [
                    {
                        "type": "string",
                        "description": "organization id (` + "`" + `me` + "`" + ` for current user, 'env' for environment)",
                        "name": "org_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "identity provider id",
                        "name": "idp_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "mapping id",
                        "name": "map_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/organization.CreateUpdateMap"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/organization.Map"
                        }
                    }
                }
            }
        },
        "/owner_request": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Owner Requests"
                ],
                "summary": "update owner request",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/ownerrequest.Row"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Owner Requests"
                ],
                "summary": "add owner request",
                "parameters": [
                    {
                        "description": "body",
                        "name": "AddReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ownerrequest.AddReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/owner_request/{id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Owner Requests"
                ],
                "summary": "update owner request",
                "parameters": [
                    {
                        "type": "string",
                        "description": "owner request id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ownerrequest.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/perms": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Permissions"
                ],
                "summary": "List all perms",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/perm.Rsp"
                            }
                        }
                    }
                }
            }
        },
        "/role": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "Roles (create custom role, rename custom role, delete custom role)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleRole",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleRole"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/role/user": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "Role users (add user to the role, remove user from role)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleRoleUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleRoleUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/scopes": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scopes"
                ],
                "summary": "List all scopes",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/scope.Rsp"
                            }
                        }
                    }
                }
            }
        },
        "/state_changes": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "State Changes",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleStateChanges",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleStateChanges"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/user": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook events"
                ],
                "summary": "Users (Add user to workspace, remove user from workspace, update User status)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swagger.ExampleUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/users/check": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get user's own data",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.CheckRow"
                        }
                    }
                }
            }
        },
        "/users/company_info": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "update company info",
                "parameters": [
                    {
                        "description": "body",
                        "name": "UpdateCompanyInfoReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UpdateCompanyInfoReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/users/{user_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get user's data",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id, 'me or 0' for own data",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.UserRow"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.Update"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/users/{user_id}/tokens": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "List user tokens",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.UserTokenListRsp"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Create user token",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UserTokenCreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/user.UserTokenRsp"
                        }
                    }
                }
            }
        },
        "/users/{user_id}/tokens/{token_id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user token",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "token_id",
                        "name": "token_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UpdateUserTokenReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.UserTokenRsp"
                        }
                    }
                }
            }
        },
        "/version": {
            "get": {
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Version"
                ],
                "summary": "Get version of API",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/http.VersionRsp"
                        }
                    }
                }
            }
        },
        "/workspaces": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "List all workspaces",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "organization id (value ` + "`" + `me` + "`" + ` for current user)",
                        "name": "org_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/workspace.ShortRsp"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Create workspace",
                "parameters": [
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/workspace.Req"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/workspace.Rsp"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Get workspace details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/workspace.Rsp"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Update workspace (name or/and status)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "ReqUpdate",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/workspace.ReqUpdate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/workspace.Rsp"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Delete workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/apis": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "List all api users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.Row"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Create api user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/api.Row"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/apis/{api_user_id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Update api user info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "api_user_id",
                        "name": "api_user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Remove api user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "api_user_id",
                        "name": "api_user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "List all groups",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "member userID to filter by",
                        "name": "member_user_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.Rsp"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Create group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/group.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/group.Rsp"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Get group info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/group.Rsp"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Update the group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/group.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/group.Rsp"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Delete group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}/apis": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Get group api users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.RowAPIUser"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Update api user list",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.UpdateUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}/owner": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Change group owner",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "ChangeOwner",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/group.ChangeOwner"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}/users": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Get group users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by roles",
                        "name": "roles",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.RowUser"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Update user list",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.UpdateUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/invites": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Invites"
                ],
                "summary": "List all invites",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "integer"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by role ids",
                        "name": "roles",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: login",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/invite.Rsp"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Invites"
                ],
                "summary": "Create invite",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/invite.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/invite.RspItem"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/invites/{invite_id}": {
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Invites"
                ],
                "summary": "Remove from the workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "invite_id",
                        "name": "invite_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "List all roles",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/role.Rsp"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Create role",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/role.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/role.Rsp"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles/available": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "List all available roles",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/role.AvailableRsp"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles/change_owner": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Change owner of the workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "ChangeOwnerReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/role.ChangeOwnerReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles/perms": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Update permissions",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdatePermName",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/role.UpdatePermName"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles/{role_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Get role info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "role id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/role.Rsp"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Update role",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "role id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/role.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/role.Rsp"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Delete role",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "role id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles/{role_id}/perms": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Update the role permissions",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "role id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateRolePermName",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/role.UpdateRolePermName"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/role.Rsp"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles/{role_id}/users": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Get role users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "role id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by roles",
                        "name": "roles",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/role.RowUser"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Update user list",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "role id",
                        "name": "role_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/role.UpdateUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "List all users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "integer"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by role ids",
                        "name": "roles",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "rsp with groups,created_at fields",
                        "name": "with",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.Rsp"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/search/{query}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Search users by id/name/login",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "query",
                        "name": "query",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.SearchRow"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/{user_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get user info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "user id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.RspItem"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user status",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UpdateStatusReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Remove from the workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "new_owner_id",
                        "name": "new_owner_id",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "integer"
                        },
                        "collectionFormat": "csv",
                        "description": "obj_owner_ids",
                        "name": "obj_owner_ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/{user_id}/groups": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "add/remove user to/from the group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateGroup",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.UpdateGroup"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/{user_id}/roles": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "add/remove user to/from the role",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateRole",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.UpdateRole"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/webhooks": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhooks"
                ],
                "summary": "List all webhooks",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/webhook.Rsp"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhooks"
                ],
                "summary": "Create webhook",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/webhook.CreateRow"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/webhook.RspItem"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/webhooks/{id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhooks"
                ],
                "summary": "Update webhook",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "webhook id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/webhook.UpdateRow"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/webhook.RspItem"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhooks"
                ],
                "summary": "Delete webhook",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "webhook id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        }
    },
    "definitions": {
        "api.CreateReq": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.Scope"
                    }
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "api.Row": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "owner_id": {
                    "type": "integer"
                },
                "owner_name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.Scope"
                    }
                },
                "status": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "api.Scope": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "default": "actors:read_only"
                },
                "type": {
                    "type": "string",
                    "default": "scopes.control"
                }
            }
        },
        "api.UpdateReq": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.Scope"
                    }
                },
                "status": {
                    "description": "API user status, available values: active/block",
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "client.Row": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "homepage": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "logo": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "redirect_uri": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "group.ChangeOwner": {
            "type": "object",
            "properties": {
                "leave": {
                    "type": "boolean"
                },
                "owner_id": {
                    "type": "integer"
                }
            }
        },
        "group.CreateReq": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "group.OwnerRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                }
            }
        },
        "group.RoleRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "my role"
                }
            }
        },
        "group.RowAPIUser": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string"
                },
                "owner_id": {
                    "type": "integer"
                },
                "owner_name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "group.RowUser": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "last_entrance": {
                    "type": "string"
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/group.RspLogin"
                    }
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/group.RoleRow"
                    }
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "group.Rsp": {
            "type": "object",
            "properties": {
                "api_users": {
                    "type": "integer",
                    "default": 1
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Default"
                },
                "owners": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/group.OwnerRow"
                    }
                },
                "user_count": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "group.RspLogin": {
            "type": "object",
            "properties": {
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "type": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "group.UpdateReq": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                }
            }
        },
        "group.UpdateUser": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "id": {
                    "description": "ID is user_id",
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "http.VersionRsp": {
            "type": "object",
            "properties": {
                "tag": {
                    "type": "string"
                }
            }
        },
        "invite.CreateReq": {
            "type": "object",
            "properties": {
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "role_id": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "invite.Rsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/invite.RspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/invite.RspMeta"
                }
            }
        },
        "invite.RspItem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "login": {
                    "type": "string"
                },
                "role_id": {
                    "type": "integer"
                },
                "role_name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "invite.RspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "license.AddAttrReq": {
            "type": "object",
            "properties": {
                "comment": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "default": "state_changes_extra_m"
                },
                "value": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "license.AdminRootLicenseDiffRsp": {
            "type": "object",
            "properties": {
                "current": {
                    "$ref": "#/definitions/license.Data"
                },
                "new": {
                    "$ref": "#/definitions/license.Data"
                },
                "ref": {
                    "type": "string"
                }
            }
        },
        "license.AdminRootLicenseRsp": {
            "type": "object",
            "properties": {
                "attributes": {
                    "$ref": "#/definitions/license.Data"
                }
            }
        },
        "license.ApprovePaymentReq": {
            "type": "object",
            "properties": {
                "files": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.FileItem"
                    }
                }
            }
        },
        "license.Attribute": {
            "type": "object",
            "properties": {
                "used": {
                    "type": "integer"
                },
                "value": {
                    "type": "integer"
                }
            }
        },
        "license.AttributeStateChanges": {
            "type": "object",
            "properties": {
                "extra": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.Extra"
                    }
                },
                "regular": {
                    "$ref": "#/definitions/license.Regular"
                }
            }
        },
        "license.Attributes": {
            "type": "object",
            "properties": {
                "rps": {
                    "$ref": "#/definitions/license.Attribute"
                },
                "state_changes": {
                    "$ref": "#/definitions/license.AttributeStateChanges"
                },
                "storage": {
                    "$ref": "#/definitions/license.Attribute"
                },
                "support": {
                    "$ref": "#/definitions/license.Attribute"
                },
                "users": {
                    "$ref": "#/definitions/license.Attribute"
                },
                "white_label": {
                    "$ref": "#/definitions/license.Attribute"
                }
            }
        },
        "license.BuyAttrReq": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "number"
                },
                "cancel_url": {
                    "type": "string"
                },
                "count": {
                    "type": "integer",
                    "example": 5
                },
                "name": {
                    "description": "available values: state_changes_extra_m,",
                    "type": "string",
                    "enum": [
                        "state_changes_extra_m"
                    ]
                },
                "payment_type": {
                    "description": "available values: online",
                    "type": "string",
                    "enum": [
                        "online"
                    ]
                },
                "success_url": {
                    "type": "string"
                }
            }
        },
        "license.CloudTariffItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.CloudTariffItemItem"
                    }
                },
                "license_type": {
                    "description": "available values: cloud, in_house, desktop",
                    "type": "string"
                },
                "max": {
                    "type": "integer"
                },
                "min": {
                    "type": "integer"
                },
                "period": {
                    "type": "integer"
                },
                "price": {
                    "type": "string",
                    "example": "0"
                },
                "year_discount": {
                    "type": "number"
                }
            }
        },
        "license.CloudTariffItemItem": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "price": {
                    "type": "string",
                    "example": "0"
                }
            }
        },
        "license.CreateCustomAttributesReq": {
            "type": "object",
            "properties": {
                "rps": {
                    "type": "integer"
                },
                "state_changes_per_period_m": {
                    "type": "integer"
                },
                "support": {
                    "type": "integer"
                },
                "users": {
                    "type": "integer"
                },
                "white_label": {
                    "type": "integer"
                }
            }
        },
        "license.CreateCustomReq": {
            "type": "object",
            "properties": {
                "attributes": {
                    "$ref": "#/definitions/license.CreateCustomAttributesReq"
                },
                "expires_at": {
                    "type": "integer"
                },
                "internal_comment": {
                    "type": "string"
                },
                "next_payment_at": {
                    "type": "integer"
                },
                "payment_type": {
                    "description": "available values: free, invoice",
                    "type": "string",
                    "enum": [
                        "free",
                        "invoice"
                    ]
                },
                "period": {
                    "description": "available values: 1, 12",
                    "type": "integer",
                    "enum": [
                        1,
                        12
                    ]
                },
                "price": {
                    "type": "number"
                },
                "tariff": {
                    "description": "available values: DEFAULT, CUSTOM, etc",
                    "type": "string",
                    "enum": [
                        "DEFAULT",
                        "CUSTOM"
                    ]
                },
                "type": {
                    "description": "available values: cloud, in_house",
                    "type": "string",
                    "enum": [
                        "cloud",
                        "in_house"
                    ]
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "license.CreateLSReq": {
            "type": "object",
            "properties": {
                "child_user_id": {
                    "type": "integer"
                },
                "comment": {
                    "type": "string"
                },
                "parent_user_id": {
                    "type": "integer"
                }
            }
        },
        "license.CreatePaymentReq": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "number"
                },
                "attr_value": {
                    "type": "integer"
                },
                "files": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.FileItem"
                    }
                },
                "license_id": {
                    "type": "integer"
                },
                "paid_before": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "license.CreatePaymentRsp": {
            "type": "object",
            "properties": {
                "payment_id": {
                    "type": "integer"
                }
            }
        },
        "license.Data": {
            "type": "object",
            "properties": {
                "account_license_module": {
                    "type": "boolean"
                },
                "cluster_id": {
                    "type": "string"
                },
                "company_id": {
                    "type": "string"
                },
                "created_time": {
                    "type": "string"
                },
                "is_allow": {
                    "type": "boolean"
                },
                "issuer": {
                    "type": "string"
                },
                "max_active_procs": {
                    "type": "integer"
                },
                "max_rpm": {
                    "type": "integer"
                },
                "max_rps": {
                    "type": "integer"
                },
                "max_scpm": {
                    "type": "integer"
                },
                "max_storage_size": {
                    "type": "integer"
                },
                "max_task_size_process": {
                    "type": "integer"
                },
                "max_task_size_state_diagram": {
                    "type": "integer"
                },
                "max_users": {
                    "type": "integer"
                },
                "min_timer": {
                    "type": "integer"
                },
                "multi_tenancy": {
                    "type": "boolean"
                },
                "time_to_expire": {
                    "type": "string"
                },
                "time_to_start": {
                    "type": "string"
                }
            }
        },
        "license.Extra": {
            "type": "object",
            "properties": {
                "balance": {
                    "type": "integer"
                },
                "date_begin": {
                    "type": "string"
                },
                "income": {
                    "type": "integer"
                }
            }
        },
        "license.FileItem": {
            "type": "object",
            "properties": {
                "link": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "license.GetStripeLinkRsp": {
            "type": "object",
            "properties": {
                "link": {
                    "type": "string"
                }
            }
        },
        "license.InvoiceInfo": {
            "type": "object",
            "properties": {
                "file_url": {
                    "type": "string"
                }
            }
        },
        "license.LSRow": {
            "type": "object",
            "properties": {
                "child_user_id": {
                    "type": "integer"
                },
                "comment": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "parent_user_id": {
                    "type": "integer"
                }
            }
        },
        "license.Owner": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                }
            }
        },
        "license.PaymentInfo": {
            "type": "object",
            "properties": {
                "invoice": {
                    "$ref": "#/definitions/license.InvoiceInfo"
                },
                "payment_id": {
                    "type": "integer"
                },
                "stripe": {
                    "$ref": "#/definitions/license.StripeInfo"
                }
            }
        },
        "license.Regular": {
            "type": "object",
            "properties": {
                "balance": {
                    "type": "integer"
                },
                "date_begin": {
                    "type": "string"
                },
                "date_end": {
                    "type": "string"
                },
                "income": {
                    "type": "integer"
                }
            }
        },
        "license.RejectPaymentReq": {
            "type": "object",
            "properties": {
                "files": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.FileItem"
                    }
                },
                "reason": {
                    "type": "string"
                }
            }
        },
        "license.Rsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.RspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/license.RspMeta"
                }
            }
        },
        "license.RspDetailed": {
            "type": "object",
            "properties": {
                "attributes": {
                    "$ref": "#/definitions/license.Attributes"
                },
                "comment": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "multitenant": {
                    "type": "boolean"
                },
                "next_payment_at": {
                    "type": "string"
                },
                "payment": {
                    "$ref": "#/definitions/license.PaymentInfo"
                },
                "payment_type": {
                    "description": "available values: invoice, online, free",
                    "type": "string",
                    "enum": [
                        "invoice",
                        "online",
                        "free"
                    ]
                },
                "period": {
                    "description": "available values: 1, 12",
                    "type": "integer"
                },
                "price": {
                    "type": "number"
                },
                "reason": {
                    "type": "string"
                },
                "status": {
                    "description": "available values: active,in_cancellation, pending, expired, canceled",
                    "type": "string",
                    "enum": [
                        "active",
                        "in_cancellation",
                        "pending",
                        " expired",
                        "canceled"
                    ]
                },
                "tariff": {
                    "description": "default value: DEFAULT, all other is custom",
                    "type": "string"
                },
                "type": {
                    "description": "available values: cloud, in_house, desktop",
                    "type": "string",
                    "enum": [
                        "cloud",
                        "in_house",
                        "desktop"
                    ]
                },
                "user_id": {
                    "type": "integer"
                },
                "workspaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.WorkspaceInfo"
                    }
                }
            }
        },
        "license.RspItem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "next_payment_at": {
                    "type": "string"
                },
                "owner": {
                    "$ref": "#/definitions/license.Owner"
                },
                "payment_type": {
                    "type": "string",
                    "enum": [
                        "invoice",
                        "stripe"
                    ]
                },
                "period": {
                    "type": "integer"
                },
                "price": {
                    "type": "number"
                },
                "reason": {
                    "type": "string"
                },
                "status": {
                    "description": "available values: active, in_cancellation, pending, expired, canceled",
                    "type": "string",
                    "enum": [
                        "active",
                        "in_cancellation",
                        " pending",
                        " expired",
                        "canceled"
                    ]
                },
                "tariff": {
                    "type": "string"
                },
                "type": {
                    "description": "available values: cloud, in_house, desktop",
                    "type": "string",
                    "enum": [
                        "cloud",
                        "in_house",
                        "desktop"
                    ]
                }
            }
        },
        "license.RspLogin": {
            "type": "object",
            "properties": {
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "type": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "license.RspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "license.RspPayment": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.RspPaymentItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/license.RspMeta"
                }
            }
        },
        "license.RspPaymentItem": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "number"
                },
                "attr_key": {
                    "type": "string"
                },
                "attr_val": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "files": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.FileItem"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "intent_type": {
                    "description": "available values: license, state_changes",
                    "type": "string",
                    "enum": [
                        "license",
                        "state_changes"
                    ]
                },
                "invoice": {
                    "$ref": "#/definitions/license.InvoiceInfo"
                },
                "license_id": {
                    "type": "integer"
                },
                "license_type": {
                    "type": "string"
                },
                "paid_at": {
                    "type": "string"
                },
                "period": {
                    "type": "integer"
                },
                "reason": {
                    "type": "string"
                },
                "ref": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "license.StripeInfo": {
            "type": "object",
            "properties": {
                "url": {
                    "type": "string"
                }
            }
        },
        "license.UpdateAttrReq": {
            "type": "object",
            "properties": {
                "comment": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "default": "rps"
                },
                "value": {
                    "type": "integer"
                }
            }
        },
        "license.UpdateCommentReq": {
            "type": "object",
            "properties": {
                "comment": {
                    "type": "string"
                }
            }
        },
        "license.UpdateRPSReq": {
            "type": "object",
            "properties": {
                "value": {
                    "type": "integer"
                }
            }
        },
        "license.UpdateReq": {
            "type": "object",
            "properties": {
                "expires_at": {
                    "description": "unix timestamp",
                    "type": "integer",
                    "default": **********
                },
                "next_payment_at": {
                    "description": "unix timestamp",
                    "type": "integer",
                    "default": **********
                }
            }
        },
        "license.UpdateStatusReq": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "User status, available values: active/block",
                    "type": "string",
                    "default": "block"
                }
            }
        },
        "license.Upload": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "string"
                }
            }
        },
        "license.UsageStateChanges": {
            "type": "object",
            "properties": {
                "usage": {
                    "type": "integer"
                }
            }
        },
        "license.User": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "last_entrance": {
                    "type": "integer"
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.RspLogin"
                    }
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "workspaces_count": {
                    "type": "integer"
                }
            }
        },
        "license.Users": {
            "type": "object",
            "properties": {
                "meta": {
                    "$ref": "#/definitions/license.RspMeta"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.User"
                    }
                }
            }
        },
        "license.WorkspaceInfo": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                }
            }
        },
        "organization.ConfigSAML": {
            "type": "object",
            "properties": {
                "idp_metadata": {
                    "type": "string"
                }
            }
        },
        "organization.CreateUpdateMap": {
            "type": "object",
            "properties": {
                "acc_attr_name": {
                    "type": "string",
                    "example": "name|email"
                },
                "attr_name": {
                    "type": "string"
                },
                "attr_value": {
                    "type": "string"
                },
                "links": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/organization.MapLink"
                    }
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/organization.MapType"
                        }
                    ],
                    "example": "const|links"
                }
            }
        },
        "organization.DomainRsp": {
            "type": "object",
            "properties": {
                "domain": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "organization.EnvIDPSet": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "enum:\"disable,authentication,authorization\"",
                    "type": "string",
                    "default": "disable",
                    "example": "authentication"
                }
            }
        },
        "organization.IDPRow": {
            "type": "object",
            "properties": {
                "config_saml": {
                    "$ref": "#/definitions/organization.ConfigSAML"
                },
                "entity_id": {
                    "type": "string"
                },
                "id": {
                    "description": "enum:\"disable,authentication,authorization\"",
                    "type": "integer"
                },
                "method": {
                    "type": "string",
                    "default": "saml",
                    "example": "saml"
                },
                "provider": {
                    "type": "string",
                    "default": "azure",
                    "example": "azure"
                },
                "provider_session_ttl": {
                    "type": "boolean"
                },
                "session_ttl": {
                    "type": "integer"
                },
                "status": {
                    "type": "string",
                    "default": "disable",
                    "example": "authentication"
                }
            }
        },
        "organization.IDPRsp": {
            "type": "object",
            "properties": {
                "entry_url": {
                    "type": "string"
                },
                "idp": {
                    "$ref": "#/definitions/organization.IDPRow"
                },
                "return_url": {
                    "type": "string"
                }
            }
        },
        "organization.IDPSet": {
            "type": "object",
            "properties": {
                "config_saml": {
                    "$ref": "#/definitions/organization.ConfigSAML"
                },
                "entity_id": {
                    "type": "string"
                },
                "method": {
                    "type": "string",
                    "default": "saml",
                    "example": "saml"
                },
                "provider": {
                    "type": "string",
                    "default": "azure",
                    "example": "azure"
                },
                "provider_session_ttl": {
                    "type": "boolean"
                },
                "session_ttl": {
                    "type": "integer"
                },
                "status": {
                    "description": "enum:\"disable,authentication,authorization\"",
                    "type": "string",
                    "default": "disable",
                    "example": "authentication"
                }
            }
        },
        "organization.Map": {
            "type": "object",
            "properties": {
                "acc_attr_name": {
                    "type": "string",
                    "example": "name|email"
                },
                "attr_name": {
                    "type": "string"
                },
                "attr_value": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "links": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/organization.MapLink"
                    }
                },
                "name": {
                    "type": "string"
                },
                "type": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/organization.MapType"
                        }
                    ],
                    "example": "const|links"
                }
            }
        },
        "organization.MapLink": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "name"
                },
                "type": {
                    "type": "string",
                    "example": "group|role"
                },
                "workspace_id": {
                    "type": "string",
                    "example": "61d35481-7ac0-4577-9dd6-37e4e5d30647"
                },
                "workspace_name": {
                    "type": "string",
                    "example": "my workspace"
                }
            }
        },
        "organization.MapType": {
            "type": "string",
            "enum": [
                "const",
                "links",
                "const_links"
            ],
            "x-enum-varnames": [
                "MapTypeConst",
                "MapTypeLinks",
                "MapTypeConstLinks"
            ]
        },
        "organization.OrgDomainReq": {
            "type": "object",
            "properties": {
                "domain": {
                    "type": "string"
                }
            }
        },
        "organization.OrgRsp": {
            "type": "object",
            "properties": {
                "domains": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "ext_id": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "modules": {
                    "type": "object",
                    "properties": {
                        "identity_providers": {
                            "type": "boolean"
                        }
                    }
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "ownerrequest.AddReq": {
            "type": "object",
            "properties": {
                "new_owner_id": {
                    "description": "The user id of the new owner",
                    "type": "integer"
                },
                "obj_owner_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "to_role_id": {
                    "description": "The role id to which the old owner is being transferred to.",
                    "type": "integer"
                },
                "workspace_id": {
                    "type": "string"
                }
            }
        },
        "ownerrequest.Row": {
            "type": "object",
            "properties": {
                "cur_owner": {
                    "$ref": "#/definitions/ownerrequest.UserRow"
                },
                "id": {
                    "type": "integer"
                },
                "new_owner": {
                    "$ref": "#/definitions/ownerrequest.UserRow"
                },
                "workspace_id": {
                    "type": "string"
                }
            }
        },
        "ownerrequest.UpdateReq": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "default": "approved|rejected|canceled"
                }
            }
        },
        "ownerrequest.UserRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "perm.Rsp": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "default": "actorBag"
                },
                "name": {
                    "type": "string",
                    "default": "actor bag"
                },
                "readonly": {
                    "type": "boolean",
                    "default": false
                },
                "type": {
                    "type": "string",
                    "default": "control"
                }
            }
        },
        "role.AvailableRsp": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Default"
                }
            }
        },
        "role.ChangeOwnerReq": {
            "type": "object",
            "properties": {
                "new_owner_id": {
                    "description": "The user id of the new owner",
                    "type": "integer"
                },
                "to_role_id": {
                    "description": "The role id to which the old owner is being transferred to.",
                    "type": "integer"
                }
            }
        },
        "role.CreateReq": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "default": "Default"
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/role.CreateReqPermName"
                    }
                },
                "users": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "role.CreateReqPermName": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "default": "actorsBag"
                },
                "type": {
                    "type": "string",
                    "default": "perms.control"
                }
            }
        },
        "role.PermName": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "default": "actorsBag"
                },
                "name": {
                    "type": "string",
                    "default": "actors bag"
                },
                "type": {
                    "type": "string",
                    "default": "perms.control"
                }
            }
        },
        "role.RowItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "my role"
                }
            }
        },
        "role.RowUser": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "last_entrance": {
                    "type": "string"
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/role.RspLogin"
                    }
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/role.RowItem"
                    }
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "role.Rsp": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Default"
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/role.PermName"
                    }
                },
                "readonly": {
                    "type": "boolean"
                },
                "users": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "role.RspLogin": {
            "type": "object",
            "properties": {
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "type": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "role.UpdatePermName": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "id": {
                    "type": "string",
                    "default": "actorsBag"
                },
                "role_id": {
                    "type": "integer",
                    "default": 1
                },
                "type": {
                    "type": "string",
                    "default": "perms.control"
                }
            }
        },
        "role.UpdateReq": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "default": "Default"
                }
            }
        },
        "role.UpdateRolePermName": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "id": {
                    "type": "string",
                    "default": "actorsBag"
                },
                "type": {
                    "type": "string",
                    "default": "perms.control"
                }
            }
        },
        "role.UpdateUser": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "id": {
                    "description": "ID is user_id",
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "scope.Rsp": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "default": "actors:read_only"
                },
                "name": {
                    "type": "string",
                    "default": "actors Read only"
                },
                "type": {
                    "type": "string",
                    "default": "control"
                }
            }
        },
        "swagger.APIKey": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "action: added, deleted, updated",
                    "type": "string",
                    "default": "added|deleted|updated"
                },
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan"
                },
                "photo": {
                    "type": "string",
                    "default": "https://google.com"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swagger.APIKeyItem"
                    }
                },
                "status": {
                    "description": "status: active, blocked",
                    "type": "string",
                    "default": "active|blocked"
                },
                "url": {
                    "type": "string",
                    "default": "https://google.com"
                }
            }
        },
        "swagger.APIKeyItem": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "swagger.ExampleAPIKey": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.APIKey"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "api_key"
                }
            }
        },
        "swagger.ExampleGroup": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.Group"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "group"
                }
            }
        },
        "swagger.ExampleGroupUser": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.GroupUser"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "group/user"
                }
            }
        },
        "swagger.ExampleInvite": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.Invite"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "invite"
                }
            }
        },
        "swagger.ExampleLogin": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.Login"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "login"
                }
            }
        },
        "swagger.ExampleRole": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.Role"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "role"
                }
            }
        },
        "swagger.ExampleRoleUser": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.RoleUser"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "role/user"
                }
            }
        },
        "swagger.ExampleStateChanges": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.StateChanges"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "state_changes"
                }
            }
        },
        "swagger.ExampleUser": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swagger.User"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "user"
                }
            }
        },
        "swagger.Group": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "action: added, deleted, updated",
                    "type": "string",
                    "default": "added|deleted|updated"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My group name"
                },
                "owner_id": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "swagger.GroupUser": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swagger.GroupUserItem"
                    }
                }
            }
        },
        "swagger.GroupUserItem": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "swagger.Invite": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "action: added, deleted",
                    "type": "string",
                    "default": "added|deleted"
                },
                "id": {
                    "type": "integer"
                },
                "login": {
                    "type": "string"
                }
            }
        },
        "swagger.Login": {
            "type": "object",
            "properties": {
                "auth_info": {},
                "id": {
                    "type": "integer"
                },
                "login": {
                    "type": "string"
                }
            }
        },
        "swagger.Role": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "action: added, deleted, updated",
                    "type": "string",
                    "default": "added|deleted|updated"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My role name"
                }
            }
        },
        "swagger.RoleUser": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swagger.RoleUserItem"
                    }
                }
            }
        },
        "swagger.RoleUserItem": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "swagger.StateChanges": {
            "type": "object",
            "properties": {
                "current": {
                    "type": "integer"
                },
                "current_extra": {
                    "type": "integer"
                },
                "license_id": {
                    "type": "integer"
                },
                "next_payment_date": {
                    "type": "string"
                },
                "value": {
                    "type": "integer"
                },
                "value_extra": {
                    "type": "integer"
                }
            }
        },
        "swagger.User": {
            "type": "object",
            "properties": {
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swagger.UserItem"
                    }
                }
            }
        },
        "swagger.UserItem": {
            "type": "object",
            "properties": {
                "action": {
                    "description": "action: added,deleted, updated",
                    "type": "string",
                    "default": "added|deleted|updated"
                },
                "id": {
                    "type": "integer"
                },
                "status": {
                    "description": "status: blocked, active",
                    "type": "string",
                    "default": "active|blocked"
                }
            }
        },
        "user.AdminRsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.AdminRspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/user.AdminRspMeta"
                }
            }
        },
        "user.AdminRspItem": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "default": "2022-10-10 09-09-09"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "last_entrance": {
                    "type": "string"
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspLogin"
                    }
                },
                "mfa": {
                    "type": "boolean",
                    "default": false
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "photo": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "default": "active"
                },
                "workspace_count": {
                    "type": "integer",
                    "default": 100
                }
            }
        },
        "user.AdminRspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "user.CheckRow": {
            "type": "object",
            "properties": {
                "license_status": {
                    "description": "license status, available values: active,expiring,canceled",
                    "type": "string"
                },
                "redirect_uri": {
                    "type": "string"
                },
                "status": {
                    "description": "status, available values: active,blocked, blocked_by_license,",
                    "type": "string"
                }
            }
        },
        "user.GroupRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "my group"
                }
            }
        },
        "user.RoleRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "my role"
                }
            }
        },
        "user.Rsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/user.RspMeta"
                }
            }
        },
        "user.RspItem": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.GroupRow"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "last_entrance": {
                    "type": "string"
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspLogin"
                    }
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RoleRow"
                    }
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "user.RspLogin": {
            "type": "object",
            "properties": {
                "create_time": {
                    "type": "integer",
                    "default": 1550153498
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "type": {
                    "type": "integer",
                    "default": 1
                },
                "type_name": {
                    "type": "string"
                }
            }
        },
        "user.RspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "user.SearchRow": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                }
            }
        },
        "user.ShortRowPermItem": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "type": {
                    "type": "string",
                    "default": "control"
                }
            }
        },
        "user.Update": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "lang": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                }
            }
        },
        "user.UpdateCompanyInfoReq": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "size": {
                    "type": "string"
                }
            }
        },
        "user.UpdateGroup": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "id": {
                    "description": "ID is group_id",
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "user.UpdateRole": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "id": {
                    "description": "ID is role_id",
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "user.UpdateStatusReq": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "User status, available values: active/blocked",
                    "type": "string",
                    "default": "blocked"
                }
            }
        },
        "user.UpdateUserTokenReq": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "user.UserRow": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_2fa": {
                    "type": "boolean"
                },
                "last_entrance": {
                    "type": "string"
                },
                "license_expires_at": {
                    "type": "string"
                },
                "license_status": {
                    "description": "license status, available values: active,expiring,canceled",
                    "type": "string"
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspLogin"
                    }
                },
                "name": {
                    "type": "string"
                },
                "options": {
                    "type": "object",
                    "properties": {
                        "disable_workspace_creation": {
                            "type": "boolean"
                        }
                    }
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.ShortRowPermItem"
                    }
                },
                "photo": {
                    "type": "string"
                },
                "status": {
                    "description": "status, available values: active,blocked, blocked_by_license,",
                    "type": "string"
                }
            }
        },
        "user.UserTokenCreateReq": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "expire_at": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "user.UserTokenListRsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.UserTokenRsp"
                    }
                }
            }
        },
        "user.UserTokenRsp": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "expire_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "webhook.CreateRow": {
            "type": "object",
            "properties": {
                "actions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "webhook.Owner": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                }
            }
        },
        "webhook.Rsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/webhook.RspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/webhook.RspMeta"
                }
            }
        },
        "webhook.RspItem": {
            "type": "object",
            "properties": {
                "actions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "created_at": {
                    "type": "string"
                },
                "failed_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "owner": {
                    "$ref": "#/definitions/webhook.Owner"
                },
                "reason": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "webhook.RspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "webhook.UpdateRow": {
            "type": "object",
            "properties": {
                "actions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "default": "active"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "workspace.AISetting": {
            "type": "object",
            "properties": {
                "api_key": {
                    "type": "string"
                },
                "provider": {
                    "type": "string"
                }
            }
        },
        "workspace.AdminRsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.AdminRspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/workspace.AdminRspMeta"
                }
            }
        },
        "workspace.AdminRspItem": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "default": "2022-10-10 09-09-09"
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "license": {
                    "$ref": "#/definitions/workspace.LicenseInfoRsp"
                },
                "license_owner": {
                    "$ref": "#/definitions/workspace.OwnerRow"
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "owner": {
                    "$ref": "#/definitions/workspace.OwnerRow"
                },
                "photo": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "default": "active"
                },
                "user_count": {
                    "type": "integer",
                    "default": 100
                }
            }
        },
        "workspace.AdminRspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "workspace.LicenseInfoRsp": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "payment_type": {
                    "type": "string",
                    "default": "free"
                }
            }
        },
        "workspace.OwnerRow": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                }
            }
        },
        "workspace.Req": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                }
            }
        },
        "workspace.ReqUpdate": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                },
                "settings": {
                    "$ref": "#/definitions/workspace.Settings"
                },
                "status": {
                    "description": "Workspace status, available values: active/block",
                    "type": "string",
                    "default": "active"
                }
            }
        },
        "workspace.RoleRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Member"
                }
            }
        },
        "workspace.Rsp": {
            "type": "object",
            "properties": {
                "api_count": {
                    "type": "integer",
                    "default": 1
                },
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "default": "2022-10-10 09-09-09"
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "group_count": {
                    "type": "integer",
                    "default": 1
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "invite_count": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "owners": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.OwnerRow"
                    }
                },
                "photo": {
                    "type": "string"
                },
                "role_count": {
                    "type": "integer",
                    "default": 1
                },
                "settings": {
                    "$ref": "#/definitions/workspace.Settings"
                },
                "status": {
                    "type": "string",
                    "default": "active"
                },
                "type": {
                    "type": "string",
                    "default": "null"
                },
                "user_count": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "workspace.RspItem": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "default": "2022-10-10 09-09-09"
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "owners": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.OwnerRow"
                    }
                },
                "photo": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.RoleRow"
                    }
                },
                "status": {
                    "type": "string",
                    "default": "active"
                },
                "user_status": {
                    "type": "string",
                    "default": "active"
                }
            }
        },
        "workspace.RspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "workspace.Settings": {
            "type": "object",
            "properties": {
                "ai_settings": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.AISetting"
                    }
                },
                "allowed_domains": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "auto_recording": {
                    "type": "boolean",
                    "default": false
                },
                "disable_invites": {
                    "type": "boolean",
                    "default": false
                },
                "file_ttl": {
                    "type": "integer"
                },
                "forbidden_domains": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "sim_client": {
                    "type": "string"
                },
                "transcription_lang": {
                    "type": "string",
                    "default": "en"
                }
            }
        },
        "workspace.ShortRsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.RspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/workspace.RspMeta"
                }
            }
        }
    },
    "securityDefinitions": {
        "BasicAuth": {
            "type": "basic"
        }
    }
}`

// SwaggerInfoface holds exported Swagger Info so clients can modify it
var SwaggerInfoface = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "face",
	SwaggerTemplate:  docTemplateface,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfoface.InstanceName(), SwaggerInfoface)
}
