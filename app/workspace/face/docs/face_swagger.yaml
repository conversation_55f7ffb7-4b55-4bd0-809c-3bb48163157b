definitions:
  api.CreateReq:
    properties:
      color:
        type: string
      name:
        type: string
      photo:
        type: string
      scopes:
        items:
          $ref: '#/definitions/api.Scope'
        type: array
      url:
        type: string
    type: object
  api.Row:
    properties:
      access_token:
        type: string
      color:
        type: string
      created_at:
        type: string
      id:
        type: integer
      name:
        type: string
      owner_id:
        type: integer
      owner_name:
        type: string
      photo:
        type: string
      scopes:
        items:
          $ref: '#/definitions/api.Scope'
        type: array
      status:
        type: string
      url:
        type: string
    type: object
  api.Scope:
    properties:
      id:
        default: actors:read_only
        type: string
      type:
        default: scopes.control
        type: string
    type: object
  api.UpdateReq:
    properties:
      color:
        type: string
      name:
        type: string
      photo:
        type: string
      scopes:
        items:
          $ref: '#/definitions/api.Scope'
        type: array
      status:
        description: 'API user status, available values: active/block'
        type: string
      url:
        type: string
    type: object
  client.Row:
    properties:
      description:
        type: string
      full_name:
        type: string
      homepage:
        type: string
      id:
        type: integer
      logo:
        type: string
      name:
        type: string
      redirect_uri:
        type: string
      type:
        type: string
    type: object
  group.ChangeOwner:
    properties:
      leave:
        type: boolean
      owner_id:
        type: integer
    type: object
  group.CreateReq:
    properties:
      name:
        type: string
      users:
        items:
          type: integer
        type: array
    type: object
  group.OwnerRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: <PERSON>ov
        type: string
    type: object
  group.RoleRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: my role
        type: string
    type: object
  group.RowAPIUser:
    properties:
      color:
        type: string
      id:
        default: 1
        type: integer
      name:
        type: string
      owner_id:
        type: integer
      owner_name:
        type: string
      photo:
        type: string
      status:
        type: string
    type: object
  group.RowUser:
    properties:
      color:
        type: string
      id:
        default: 1
        type: integer
      last_entrance:
        type: string
      logins:
        items:
          $ref: '#/definitions/group.RspLogin'
        type: array
      name:
        type: string
      photo:
        type: string
      roles:
        items:
          $ref: '#/definitions/group.RoleRow'
        type: array
      status:
        type: string
    type: object
  group.Rsp:
    properties:
      api_users:
        default: 1
        type: integer
      created_at:
        type: string
      id:
        default: 1
        type: integer
      name:
        default: Default
        type: string
      owners:
        items:
          $ref: '#/definitions/group.OwnerRow'
        type: array
      user_count:
        default: 1
        type: integer
    type: object
  group.RspLogin:
    properties:
      login:
        default: <EMAIL>
        type: string
      type:
        default: 1
        type: integer
    type: object
  group.UpdateReq:
    properties:
      name:
        type: string
    type: object
  group.UpdateUser:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      id:
        default: 1
        description: ID is user_id
        type: integer
    type: object
  http.VersionRsp:
    properties:
      tag:
        type: string
    type: object
  invite.CreateReq:
    properties:
      login:
        default: <EMAIL>
        type: string
      role_id:
        default: 1
        type: integer
    type: object
  invite.Rsp:
    properties:
      data:
        items:
          $ref: '#/definitions/invite.RspItem'
        type: array
      meta:
        $ref: '#/definitions/invite.RspMeta'
    type: object
  invite.RspItem:
    properties:
      created_at:
        type: string
      id:
        type: integer
      login:
        type: string
      role_id:
        type: integer
      role_name:
        type: string
      status:
        type: string
    type: object
  invite.RspMeta:
    properties:
      total:
        type: integer
    type: object
  license.AddAttrReq:
    properties:
      comment:
        type: string
      name:
        default: state_changes_extra_m
        type: string
      value:
        default: 1
        type: integer
    type: object
  license.AdminRootLicenseDiffRsp:
    properties:
      current:
        $ref: '#/definitions/license.Data'
      new:
        $ref: '#/definitions/license.Data'
      ref:
        type: string
    type: object
  license.AdminRootLicenseRsp:
    properties:
      attributes:
        $ref: '#/definitions/license.Data'
    type: object
  license.ApprovePaymentReq:
    properties:
      files:
        items:
          $ref: '#/definitions/license.FileItem'
        type: array
    type: object
  license.Attribute:
    properties:
      used:
        type: integer
      value:
        type: integer
    type: object
  license.AttributeStateChanges:
    properties:
      extra:
        items:
          $ref: '#/definitions/license.Extra'
        type: array
      regular:
        $ref: '#/definitions/license.Regular'
    type: object
  license.Attributes:
    properties:
      rps:
        $ref: '#/definitions/license.Attribute'
      state_changes:
        $ref: '#/definitions/license.AttributeStateChanges'
      storage:
        $ref: '#/definitions/license.Attribute'
      support:
        $ref: '#/definitions/license.Attribute'
      users:
        $ref: '#/definitions/license.Attribute'
      white_label:
        $ref: '#/definitions/license.Attribute'
    type: object
  license.BuyAttrReq:
    properties:
      amount:
        type: number
      cancel_url:
        type: string
      count:
        example: 5
        type: integer
      name:
        description: 'available values: state_changes_extra_m,'
        enum:
        - state_changes_extra_m
        type: string
      payment_type:
        description: 'available values: online'
        enum:
        - online
        type: string
      success_url:
        type: string
    type: object
  license.CloudTariffItem:
    properties:
      id:
        type: string
      items:
        items:
          $ref: '#/definitions/license.CloudTariffItemItem'
        type: array
      license_type:
        description: 'available values: cloud, in_house, desktop'
        type: string
      max:
        type: integer
      min:
        type: integer
      period:
        type: integer
      price:
        example: "0"
        type: string
      year_discount:
        type: number
    type: object
  license.CloudTariffItemItem:
    properties:
      count:
        type: integer
      price:
        example: "0"
        type: string
    type: object
  license.CreateCustomAttributesReq:
    properties:
      rps:
        type: integer
      state_changes_per_period_m:
        type: integer
      support:
        type: integer
      users:
        type: integer
      white_label:
        type: integer
    type: object
  license.CreateCustomReq:
    properties:
      attributes:
        $ref: '#/definitions/license.CreateCustomAttributesReq'
      expires_at:
        type: integer
      internal_comment:
        type: string
      next_payment_at:
        type: integer
      payment_type:
        description: 'available values: free, invoice'
        enum:
        - free
        - invoice
        type: string
      period:
        description: 'available values: 1, 12'
        enum:
        - 1
        - 12
        type: integer
      price:
        type: number
      tariff:
        description: 'available values: DEFAULT, CUSTOM, etc'
        enum:
        - DEFAULT
        - CUSTOM
        type: string
      type:
        description: 'available values: cloud, in_house'
        enum:
        - cloud
        - in_house
        type: string
      user_id:
        type: integer
    type: object
  license.CreateLSReq:
    properties:
      child_user_id:
        type: integer
      comment:
        type: string
      parent_user_id:
        type: integer
    type: object
  license.CreatePaymentReq:
    properties:
      amount:
        type: number
      attr_value:
        type: integer
      files:
        items:
          $ref: '#/definitions/license.FileItem'
        type: array
      license_id:
        type: integer
      paid_before:
        type: integer
      status:
        type: string
      type:
        type: string
    type: object
  license.CreatePaymentRsp:
    properties:
      payment_id:
        type: integer
    type: object
  license.Data:
    properties:
      account_license_module:
        type: boolean
      cluster_id:
        type: string
      company_id:
        type: string
      created_time:
        type: string
      is_allow:
        type: boolean
      issuer:
        type: string
      max_active_procs:
        type: integer
      max_rpm:
        type: integer
      max_rps:
        type: integer
      max_scpm:
        type: integer
      max_storage_size:
        type: integer
      max_task_size_process:
        type: integer
      max_task_size_state_diagram:
        type: integer
      max_users:
        type: integer
      min_timer:
        type: integer
      multi_tenancy:
        type: boolean
      time_to_expire:
        type: string
      time_to_start:
        type: string
    type: object
  license.Extra:
    properties:
      balance:
        type: integer
      date_begin:
        type: string
      income:
        type: integer
    type: object
  license.FileItem:
    properties:
      link:
        type: string
      title:
        type: string
    type: object
  license.GetStripeLinkRsp:
    properties:
      link:
        type: string
    type: object
  license.InvoiceInfo:
    properties:
      file_url:
        type: string
    type: object
  license.LSRow:
    properties:
      child_user_id:
        type: integer
      comment:
        type: string
      created_at:
        type: string
      parent_user_id:
        type: integer
    type: object
  license.Owner:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: Ivan Ivanov
        type: string
    type: object
  license.PaymentInfo:
    properties:
      invoice:
        $ref: '#/definitions/license.InvoiceInfo'
      payment_id:
        type: integer
      stripe:
        $ref: '#/definitions/license.StripeInfo'
    type: object
  license.Regular:
    properties:
      balance:
        type: integer
      date_begin:
        type: string
      date_end:
        type: string
      income:
        type: integer
    type: object
  license.RejectPaymentReq:
    properties:
      files:
        items:
          $ref: '#/definitions/license.FileItem'
        type: array
      reason:
        type: string
    type: object
  license.Rsp:
    properties:
      data:
        items:
          $ref: '#/definitions/license.RspItem'
        type: array
      meta:
        $ref: '#/definitions/license.RspMeta'
    type: object
  license.RspDetailed:
    properties:
      attributes:
        $ref: '#/definitions/license.Attributes'
      comment:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: integer
      multitenant:
        type: boolean
      next_payment_at:
        type: string
      payment:
        $ref: '#/definitions/license.PaymentInfo'
      payment_type:
        description: 'available values: invoice, online, free'
        enum:
        - invoice
        - online
        - free
        type: string
      period:
        description: 'available values: 1, 12'
        type: integer
      price:
        type: number
      reason:
        type: string
      status:
        description: 'available values: active,in_cancellation, pending, expired,
          canceled'
        enum:
        - active
        - in_cancellation
        - pending
        - ' expired'
        - canceled
        type: string
      tariff:
        description: 'default value: DEFAULT, all other is custom'
        type: string
      type:
        description: 'available values: cloud, in_house, desktop'
        enum:
        - cloud
        - in_house
        - desktop
        type: string
      user_id:
        type: integer
      workspaces:
        items:
          $ref: '#/definitions/license.WorkspaceInfo'
        type: array
    type: object
  license.RspItem:
    properties:
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: integer
      next_payment_at:
        type: string
      owner:
        $ref: '#/definitions/license.Owner'
      payment_type:
        enum:
        - invoice
        - stripe
        type: string
      period:
        type: integer
      price:
        type: number
      reason:
        type: string
      status:
        description: 'available values: active, in_cancellation, pending, expired,
          canceled'
        enum:
        - active
        - in_cancellation
        - ' pending'
        - ' expired'
        - canceled
        type: string
      tariff:
        type: string
      type:
        description: 'available values: cloud, in_house, desktop'
        enum:
        - cloud
        - in_house
        - desktop
        type: string
    type: object
  license.RspLogin:
    properties:
      login:
        default: <EMAIL>
        type: string
      type:
        default: 1
        type: integer
    type: object
  license.RspMeta:
    properties:
      total:
        type: integer
    type: object
  license.RspPayment:
    properties:
      data:
        items:
          $ref: '#/definitions/license.RspPaymentItem'
        type: array
      meta:
        $ref: '#/definitions/license.RspMeta'
    type: object
  license.RspPaymentItem:
    properties:
      amount:
        type: number
      attr_key:
        type: string
      attr_val:
        type: integer
      created_at:
        type: string
      files:
        items:
          $ref: '#/definitions/license.FileItem'
        type: array
      id:
        type: integer
      intent_type:
        description: 'available values: license, state_changes'
        enum:
        - license
        - state_changes
        type: string
      invoice:
        $ref: '#/definitions/license.InvoiceInfo'
      license_id:
        type: integer
      license_type:
        type: string
      paid_at:
        type: string
      period:
        type: integer
      reason:
        type: string
      ref:
        type: string
      status:
        type: string
      type:
        type: string
    type: object
  license.StripeInfo:
    properties:
      url:
        type: string
    type: object
  license.UpdateAttrReq:
    properties:
      comment:
        type: string
      name:
        default: rps
        type: string
      value:
        type: integer
    type: object
  license.UpdateCommentReq:
    properties:
      comment:
        type: string
    type: object
  license.UpdateRPSReq:
    properties:
      value:
        type: integer
    type: object
  license.UpdateReq:
    properties:
      expires_at:
        default: **********
        description: unix timestamp
        type: integer
      next_payment_at:
        default: **********
        description: unix timestamp
        type: integer
    type: object
  license.UpdateStatusReq:
    properties:
      status:
        default: block
        description: 'User status, available values: active/block'
        type: string
    type: object
  license.Upload:
    properties:
      data:
        type: string
    type: object
  license.UsageStateChanges:
    properties:
      usage:
        type: integer
    type: object
  license.User:
    properties:
      id:
        type: integer
      last_entrance:
        type: integer
      logins:
        items:
          $ref: '#/definitions/license.RspLogin'
        type: array
      name:
        type: string
      photo:
        type: string
      status:
        type: string
      workspaces_count:
        type: integer
    type: object
  license.Users:
    properties:
      meta:
        $ref: '#/definitions/license.RspMeta'
      users:
        items:
          $ref: '#/definitions/license.User'
        type: array
    type: object
  license.WorkspaceInfo:
    properties:
      id:
        type: string
    type: object
  organization.ConfigSAML:
    properties:
      idp_metadata:
        type: string
    type: object
  organization.CreateUpdateMap:
    properties:
      acc_attr_name:
        example: name|email
        type: string
      attr_name:
        type: string
      attr_value:
        type: string
      links:
        items:
          $ref: '#/definitions/organization.MapLink'
        type: array
      name:
        type: string
      type:
        allOf:
        - $ref: '#/definitions/organization.MapType'
        example: const|links
    type: object
  organization.DomainRsp:
    properties:
      domain:
        type: string
      id:
        type: integer
    type: object
  organization.EnvIDPSet:
    properties:
      status:
        default: disable
        description: enum:"disable,authentication,authorization"
        example: authentication
        type: string
    type: object
  organization.IDPRow:
    properties:
      config_saml:
        $ref: '#/definitions/organization.ConfigSAML'
      entity_id:
        type: string
      id:
        description: enum:"disable,authentication,authorization"
        type: integer
      method:
        default: saml
        example: saml
        type: string
      provider:
        default: azure
        example: azure
        type: string
      provider_session_ttl:
        type: boolean
      session_ttl:
        type: integer
      status:
        default: disable
        example: authentication
        type: string
    type: object
  organization.IDPRsp:
    properties:
      entry_url:
        type: string
      idp:
        $ref: '#/definitions/organization.IDPRow'
      return_url:
        type: string
    type: object
  organization.IDPSet:
    properties:
      config_saml:
        $ref: '#/definitions/organization.ConfigSAML'
      entity_id:
        type: string
      method:
        default: saml
        example: saml
        type: string
      provider:
        default: azure
        example: azure
        type: string
      provider_session_ttl:
        type: boolean
      session_ttl:
        type: integer
      status:
        default: disable
        description: enum:"disable,authentication,authorization"
        example: authentication
        type: string
    type: object
  organization.Map:
    properties:
      acc_attr_name:
        example: name|email
        type: string
      attr_name:
        type: string
      attr_value:
        type: string
      id:
        type: integer
      links:
        items:
          $ref: '#/definitions/organization.MapLink'
        type: array
      name:
        type: string
      type:
        allOf:
        - $ref: '#/definitions/organization.MapType'
        example: const|links
    type: object
  organization.MapLink:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: name
        type: string
      type:
        example: group|role
        type: string
      workspace_id:
        example: 61d35481-7ac0-4577-9dd6-37e4e5d30647
        type: string
      workspace_name:
        example: my workspace
        type: string
    type: object
  organization.MapType:
    enum:
    - const
    - links
    - const_links
    type: string
    x-enum-varnames:
    - MapTypeConst
    - MapTypeLinks
    - MapTypeConstLinks
  organization.OrgDomainReq:
    properties:
      domain:
        type: string
    type: object
  organization.OrgRsp:
    properties:
      domains:
        items:
          type: string
        type: array
      ext_id:
        type: string
      id:
        type: integer
      modules:
        properties:
          identity_providers:
            type: boolean
        type: object
      name:
        type: string
    type: object
  ownerrequest.AddReq:
    properties:
      new_owner_id:
        description: The user id of the new owner
        type: integer
      obj_owner_ids:
        items:
          type: integer
        type: array
      to_role_id:
        description: The role id to which the old owner is being transferred to.
        type: integer
      workspace_id:
        type: string
    type: object
  ownerrequest.Row:
    properties:
      cur_owner:
        $ref: '#/definitions/ownerrequest.UserRow'
      id:
        type: integer
      new_owner:
        $ref: '#/definitions/ownerrequest.UserRow'
      workspace_id:
        type: string
    type: object
  ownerrequest.UpdateReq:
    properties:
      status:
        default: approved|rejected|canceled
        type: string
    type: object
  ownerrequest.UserRow:
    properties:
      id:
        type: integer
      name:
        type: string
    type: object
  perm.Rsp:
    properties:
      id:
        default: actorBag
        type: string
      name:
        default: actor bag
        type: string
      readonly:
        default: false
        type: boolean
      type:
        default: control
        type: string
    type: object
  role.AvailableRsp:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: Default
        type: string
    type: object
  role.ChangeOwnerReq:
    properties:
      new_owner_id:
        description: The user id of the new owner
        type: integer
      to_role_id:
        description: The role id to which the old owner is being transferred to.
        type: integer
    type: object
  role.CreateReq:
    properties:
      name:
        default: Default
        type: string
      perms:
        items:
          $ref: '#/definitions/role.CreateReqPermName'
        type: array
      users:
        items:
          type: integer
        type: array
    type: object
  role.CreateReqPermName:
    properties:
      id:
        default: actorsBag
        type: string
      type:
        default: perms.control
        type: string
    type: object
  role.PermName:
    properties:
      id:
        default: actorsBag
        type: string
      name:
        default: actors bag
        type: string
      type:
        default: perms.control
        type: string
    type: object
  role.RowItem:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: my role
        type: string
    type: object
  role.RowUser:
    properties:
      color:
        type: string
      id:
        default: 1
        type: integer
      last_entrance:
        type: string
      logins:
        items:
          $ref: '#/definitions/role.RspLogin'
        type: array
      name:
        type: string
      photo:
        type: string
      roles:
        items:
          $ref: '#/definitions/role.RowItem'
        type: array
      status:
        type: string
    type: object
  role.Rsp:
    properties:
      created_at:
        type: string
      id:
        default: 1
        type: integer
      name:
        default: Default
        type: string
      perms:
        items:
          $ref: '#/definitions/role.PermName'
        type: array
      readonly:
        type: boolean
      users:
        default: 1
        type: integer
    type: object
  role.RspLogin:
    properties:
      login:
        default: <EMAIL>
        type: string
      type:
        default: 1
        type: integer
    type: object
  role.UpdatePermName:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      id:
        default: actorsBag
        type: string
      role_id:
        default: 1
        type: integer
      type:
        default: perms.control
        type: string
    type: object
  role.UpdateReq:
    properties:
      name:
        default: Default
        type: string
    type: object
  role.UpdateRolePermName:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      id:
        default: actorsBag
        type: string
      type:
        default: perms.control
        type: string
    type: object
  role.UpdateUser:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      id:
        default: 1
        description: ID is user_id
        type: integer
    type: object
  scope.Rsp:
    properties:
      id:
        default: actors:read_only
        type: string
      name:
        default: actors Read only
        type: string
      type:
        default: control
        type: string
    type: object
  swagger.APIKey:
    properties:
      action:
        default: added|deleted|updated
        description: 'action: added, deleted, updated'
        type: string
      color:
        default: '#000000'
        type: string
      id:
        default: 1
        type: integer
      name:
        default: Ivan
        type: string
      photo:
        default: https://google.com
        type: string
      scopes:
        items:
          $ref: '#/definitions/swagger.APIKeyItem'
        type: array
      status:
        default: active|blocked
        description: 'status: active, blocked'
        type: string
      url:
        default: https://google.com
        type: string
    type: object
  swagger.APIKeyItem:
    properties:
      ids:
        items:
          type: string
        type: array
      type:
        type: string
    type: object
  swagger.ExampleAPIKey:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.APIKey'
      id:
        default: 1
        type: integer
      name:
        default: api_key
        type: string
    type: object
  swagger.ExampleGroup:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.Group'
      id:
        default: 1
        type: integer
      name:
        default: group
        type: string
    type: object
  swagger.ExampleGroupUser:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.GroupUser'
      id:
        default: 1
        type: integer
      name:
        default: group/user
        type: string
    type: object
  swagger.ExampleInvite:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.Invite'
      id:
        default: 1
        type: integer
      name:
        default: invite
        type: string
    type: object
  swagger.ExampleLogin:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.Login'
      id:
        default: 1
        type: integer
      name:
        default: login
        type: string
    type: object
  swagger.ExampleRole:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.Role'
      id:
        default: 1
        type: integer
      name:
        default: role
        type: string
    type: object
  swagger.ExampleRoleUser:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.RoleUser'
      id:
        default: 1
        type: integer
      name:
        default: role/user
        type: string
    type: object
  swagger.ExampleStateChanges:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.StateChanges'
      id:
        default: 1
        type: integer
      name:
        default: state_changes
        type: string
    type: object
  swagger.ExampleUser:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swagger.User'
      id:
        default: 1
        type: integer
      name:
        default: user
        type: string
    type: object
  swagger.Group:
    properties:
      action:
        default: added|deleted|updated
        description: 'action: added, deleted, updated'
        type: string
      id:
        default: 1
        type: integer
      name:
        default: My group name
        type: string
      owner_id:
        default: 1
        type: integer
    type: object
  swagger.GroupUser:
    properties:
      id:
        default: 1
        type: integer
      users:
        items:
          $ref: '#/definitions/swagger.GroupUserItem'
        type: array
    type: object
  swagger.GroupUserItem:
    properties:
      active:
        type: boolean
      id:
        type: integer
      type:
        type: string
    type: object
  swagger.Invite:
    properties:
      action:
        default: added|deleted
        description: 'action: added, deleted'
        type: string
      id:
        type: integer
      login:
        type: string
    type: object
  swagger.Login:
    properties:
      auth_info: {}
      id:
        type: integer
      login:
        type: string
    type: object
  swagger.Role:
    properties:
      action:
        default: added|deleted|updated
        description: 'action: added, deleted, updated'
        type: string
      id:
        default: 1
        type: integer
      name:
        default: My role name
        type: string
    type: object
  swagger.RoleUser:
    properties:
      id:
        default: 1
        type: integer
      users:
        items:
          $ref: '#/definitions/swagger.RoleUserItem'
        type: array
    type: object
  swagger.RoleUserItem:
    properties:
      active:
        type: boolean
      id:
        type: integer
    type: object
  swagger.StateChanges:
    properties:
      current:
        type: integer
      current_extra:
        type: integer
      license_id:
        type: integer
      next_payment_date:
        type: string
      value:
        type: integer
      value_extra:
        type: integer
    type: object
  swagger.User:
    properties:
      users:
        items:
          $ref: '#/definitions/swagger.UserItem'
        type: array
    type: object
  swagger.UserItem:
    properties:
      action:
        default: added|deleted|updated
        description: 'action: added,deleted, updated'
        type: string
      id:
        type: integer
      status:
        default: active|blocked
        description: 'status: blocked, active'
        type: string
    type: object
  user.AdminRsp:
    properties:
      data:
        items:
          $ref: '#/definitions/user.AdminRspItem'
        type: array
      meta:
        $ref: '#/definitions/user.AdminRspMeta'
    type: object
  user.AdminRspItem:
    properties:
      color:
        type: string
      created_at:
        default: 2022-10-10 09-09-09
        type: string
      id:
        default: 1
        type: integer
      last_entrance:
        type: string
      logins:
        items:
          $ref: '#/definitions/user.RspLogin'
        type: array
      mfa:
        default: false
        type: boolean
      name:
        default: My workspace
        type: string
      photo:
        type: string
      status:
        default: active
        type: string
      workspace_count:
        default: 100
        type: integer
    type: object
  user.AdminRspMeta:
    properties:
      total:
        type: integer
    type: object
  user.CheckRow:
    properties:
      license_status:
        description: 'license status, available values: active,expiring,canceled'
        type: string
      redirect_uri:
        type: string
      status:
        description: 'status, available values: active,blocked, blocked_by_license,'
        type: string
    type: object
  user.GroupRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: my group
        type: string
    type: object
  user.RoleRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: my role
        type: string
    type: object
  user.Rsp:
    properties:
      data:
        items:
          $ref: '#/definitions/user.RspItem'
        type: array
      meta:
        $ref: '#/definitions/user.RspMeta'
    type: object
  user.RspItem:
    properties:
      color:
        type: string
      created_at:
        type: string
      groups:
        items:
          $ref: '#/definitions/user.GroupRow'
        type: array
      id:
        type: integer
      last_entrance:
        type: string
      logins:
        items:
          $ref: '#/definitions/user.RspLogin'
        type: array
      name:
        type: string
      photo:
        type: string
      roles:
        items:
          $ref: '#/definitions/user.RoleRow'
        type: array
      status:
        type: string
    type: object
  user.RspLogin:
    properties:
      create_time:
        default: 1550153498
        type: integer
      id:
        default: 1
        type: integer
      login:
        default: <EMAIL>
        type: string
      type:
        default: 1
        type: integer
      type_name:
        type: string
    type: object
  user.RspMeta:
    properties:
      total:
        type: integer
    type: object
  user.SearchRow:
    properties:
      color:
        default: '#000000'
        type: string
      id:
        type: integer
      name:
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
    type: object
  user.ShortRowPermItem:
    properties:
      ids:
        items:
          type: string
        type: array
      type:
        default: control
        type: string
    type: object
  user.Update:
    properties:
      color:
        default: '#000000'
        type: string
      lang:
        type: string
      name:
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
    type: object
  user.UpdateCompanyInfoReq:
    properties:
      name:
        type: string
      size:
        type: string
    type: object
  user.UpdateGroup:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      id:
        default: 1
        description: ID is group_id
        type: integer
    type: object
  user.UpdateRole:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      id:
        default: 1
        description: ID is role_id
        type: integer
    type: object
  user.UpdateStatusReq:
    properties:
      status:
        default: blocked
        description: 'User status, available values: active/blocked'
        type: string
    type: object
  user.UpdateUserTokenReq:
    properties:
      description:
        type: string
      name:
        type: string
      status:
        type: string
    type: object
  user.UserRow:
    properties:
      color:
        type: string
      id:
        type: integer
      is_2fa:
        type: boolean
      last_entrance:
        type: string
      license_expires_at:
        type: string
      license_status:
        description: 'license status, available values: active,expiring,canceled'
        type: string
      logins:
        items:
          $ref: '#/definitions/user.RspLogin'
        type: array
      name:
        type: string
      options:
        properties:
          disable_workspace_creation:
            type: boolean
        type: object
      perms:
        items:
          $ref: '#/definitions/user.ShortRowPermItem'
        type: array
      photo:
        type: string
      status:
        description: 'status, available values: active,blocked, blocked_by_license,'
        type: string
    type: object
  user.UserTokenCreateReq:
    properties:
      description:
        type: string
      expire_at:
        type: string
      name:
        type: string
    type: object
  user.UserTokenListRsp:
    properties:
      data:
        items:
          $ref: '#/definitions/user.UserTokenRsp'
        type: array
    type: object
  user.UserTokenRsp:
    properties:
      created_at:
        type: string
      description:
        type: string
      expire_at:
        type: string
      id:
        type: string
      name:
        type: string
      status:
        type: string
      updated_at:
        type: string
    type: object
  webhook.CreateRow:
    properties:
      actions:
        items:
          type: string
        type: array
      url:
        type: string
    type: object
  webhook.Owner:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: Ivan Ivanov
        type: string
    type: object
  webhook.Rsp:
    properties:
      data:
        items:
          $ref: '#/definitions/webhook.RspItem'
        type: array
      meta:
        $ref: '#/definitions/webhook.RspMeta'
    type: object
  webhook.RspItem:
    properties:
      actions:
        items:
          type: string
        type: array
      created_at:
        type: string
      failed_at:
        type: string
      id:
        type: integer
      name:
        type: string
      owner:
        $ref: '#/definitions/webhook.Owner'
      reason:
        type: string
      status:
        type: string
      url:
        type: string
    type: object
  webhook.RspMeta:
    properties:
      total:
        type: integer
    type: object
  webhook.UpdateRow:
    properties:
      actions:
        items:
          type: string
        type: array
      name:
        type: string
      status:
        default: active
        type: string
      url:
        type: string
    type: object
  workspace.AISetting:
    properties:
      api_key:
        type: string
      provider:
        type: string
    type: object
  workspace.AdminRsp:
    properties:
      data:
        items:
          $ref: '#/definitions/workspace.AdminRspItem'
        type: array
      meta:
        $ref: '#/definitions/workspace.AdminRspMeta'
    type: object
  workspace.AdminRspItem:
    properties:
      color:
        type: string
      created_at:
        default: 2022-10-10 09-09-09
        type: string
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      id:
        default: 1
        type: integer
      license:
        $ref: '#/definitions/workspace.LicenseInfoRsp'
      license_owner:
        $ref: '#/definitions/workspace.OwnerRow'
      name:
        default: My workspace
        type: string
      owner:
        $ref: '#/definitions/workspace.OwnerRow'
      photo:
        type: string
      status:
        default: active
        type: string
      user_count:
        default: 100
        type: integer
    type: object
  workspace.AdminRspMeta:
    properties:
      total:
        type: integer
    type: object
  workspace.LicenseInfoRsp:
    properties:
      id:
        default: 1
        type: integer
      payment_type:
        default: free
        type: string
    type: object
  workspace.OwnerRow:
    properties:
      color:
        default: '#000000'
        type: string
      id:
        default: 1
        type: integer
      name:
        default: Ivan Ivanov
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
    type: object
  workspace.Req:
    properties:
      color:
        default: '#000000'
        type: string
      name:
        default: My workspace
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
    type: object
  workspace.ReqUpdate:
    properties:
      color:
        default: '#000000'
        type: string
      name:
        default: My workspace
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
      settings:
        $ref: '#/definitions/workspace.Settings'
      status:
        default: active
        description: 'Workspace status, available values: active/block'
        type: string
    type: object
  workspace.RoleRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: Member
        type: string
    type: object
  workspace.Rsp:
    properties:
      api_count:
        default: 1
        type: integer
      color:
        type: string
      created_at:
        default: 2022-10-10 09-09-09
        type: string
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      group_count:
        default: 1
        type: integer
      id:
        default: 1
        type: integer
      invite_count:
        default: 1
        type: integer
      name:
        default: My workspace
        type: string
      owners:
        items:
          $ref: '#/definitions/workspace.OwnerRow'
        type: array
      photo:
        type: string
      role_count:
        default: 1
        type: integer
      settings:
        $ref: '#/definitions/workspace.Settings'
      status:
        default: active
        type: string
      type:
        default: "null"
        type: string
      user_count:
        default: 1
        type: integer
    type: object
  workspace.RspItem:
    properties:
      color:
        type: string
      created_at:
        default: 2022-10-10 09-09-09
        type: string
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      id:
        default: 1
        type: integer
      name:
        default: My workspace
        type: string
      owners:
        items:
          $ref: '#/definitions/workspace.OwnerRow'
        type: array
      photo:
        type: string
      roles:
        items:
          $ref: '#/definitions/workspace.RoleRow'
        type: array
      status:
        default: active
        type: string
      user_status:
        default: active
        type: string
    type: object
  workspace.RspMeta:
    properties:
      total:
        type: integer
    type: object
  workspace.Settings:
    properties:
      ai_settings:
        items:
          $ref: '#/definitions/workspace.AISetting'
        type: array
      allowed_domains:
        items:
          type: string
        type: array
      auto_recording:
        default: false
        type: boolean
      disable_invites:
        default: false
        type: boolean
      file_ttl:
        type: integer
      forbidden_domains:
        items:
          type: string
        type: array
      sim_client:
        type: string
      transcription_lang:
        default: en
        type: string
    type: object
  workspace.ShortRsp:
    properties:
      data:
        items:
          $ref: '#/definitions/workspace.RspItem'
        type: array
      meta:
        $ref: '#/definitions/workspace.RspMeta'
    type: object
info:
  contact: {}
paths:
  /admin/licenses:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: CreateCustomReq
        required: true
        schema:
          $ref: '#/definitions/license.CreateCustomReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Create new custom license
      tags:
      - License management
  /admin/licenses/{license_id}/attrs/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: integer
      - description: body
        in: body
        name: AddAttrReq
        required: true
        schema:
          $ref: '#/definitions/license.AddAttrReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Add license attr
      tags:
      - License management
  /admin/licenses/{license_id}/attrs/update:
    put:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: integer
      - description: body
        in: body
        name: UpdateAttrReq
        required: true
        schema:
          $ref: '#/definitions/license.UpdateAttrReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update license attr
      tags:
      - License management
  /admin/licenses/{license_id}/cancel:
    post:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Cancel license
      tags:
      - License management
  /admin/licenses/{license_id}/dates:
    put:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: integer
      - description: body
        in: body
        name: UpdateReq
        required: true
        schema:
          $ref: '#/definitions/license.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update license dates
      tags:
      - License management
  /admin/licenses/import:
    post:
      consumes:
      - multipart/form-data
      operationId: file.upload
      parameters:
      - description: this is a test file
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Import licenses from file [DEPRECATED]
      tags:
      - License management
  /admin/licenses/payments:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: CreatePaymentReq
        required: true
        schema:
          $ref: '#/definitions/license.CreatePaymentReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/license.CreatePaymentRsp'
      security:
      - BasicAuth: []
      summary: Create payment
      tags:
      - License management
  /admin/licenses/payments/{payment_id}/approve:
    post:
      consumes:
      - application/json
      parameters:
      - description: payment id
        in: path
        name: payment_id
        required: true
        type: integer
      - description: body
        in: body
        name: ApprovePaymentReq
        required: true
        schema:
          $ref: '#/definitions/license.ApprovePaymentReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Approve payment
      tags:
      - License management
  /admin/licenses/payments/{payment_id}/reject:
    post:
      consumes:
      - application/json
      parameters:
      - description: payment id
        in: path
        name: payment_id
        required: true
        type: integer
      - description: body
        in: body
        name: RejectPaymentReq
        required: true
        schema:
          $ref: '#/definitions/license.RejectPaymentReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Reject payment
      tags:
      - License management
  /admin/licenses/root:
    get:
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.AdminRootLicenseRsp'
      security:
      - BasicAuth: []
      summary: Get current root license
      tags:
      - License management
  /admin/licenses/root/{ref}/apply:
    post:
      parameters:
      - description: ref
        in: path
        name: ref
        required: true
        type: string
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: apply new license by ref
      tags:
      - License management
  /admin/licenses/root/{ref}/diff:
    get:
      parameters:
      - description: ref
        in: path
        name: ref
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.AdminRootLicenseDiffRsp'
      security:
      - BasicAuth: []
      summary: Diff between the new license and the current one
      tags:
      - License management
  /admin/licenses/root/upload:
    post:
      parameters:
      - description: body
        in: body
        name: Upload
        required: true
        schema:
          $ref: '#/definitions/license.Upload'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.AdminRootLicenseDiffRsp'
      security:
      - BasicAuth: []
      summary: Upload license file
      tags:
      - License management
  /admin/licenses/sharing:
    get:
      consumes:
      - application/json
      parameters:
      - description: parent user id
        in: query
        name: parent_user_id
        type: integer
      - description: child user id
        in: query
        name: child_user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/license.LSRow'
            type: array
      security:
      - BasicAuth: []
      summary: 'License Sharing: List all links'
      tags:
      - License management
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: CreateLSReq
        required: true
        schema:
          $ref: '#/definitions/license.CreateLSReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: 'License Sharing: Share license with user'
      tags:
      - License management
  /admin/licenses/sharing/{child_user_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: child user id
        in: path
        name: child_user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: 'License Sharing: Delete sharing'
      tags:
      - License management
  /admin/licenses/used_state_changes:
    get:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: query
        name: license_id
        type: string
      - description: user id
        in: query
        name: user_id
        type: string
      - description: from 2006-12-01
        in: query
        name: from
        required: true
        type: string
      - description: to 2006-12-31
        in: query
        name: to
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.UsageStateChanges'
      security:
      - BasicAuth: []
      summary: Provide used state_changes for the period
      tags:
      - License
  /admin/licenses/users/{user_id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RspDetailed'
      security:
      - BasicAuth: []
      summary: Get license by user
      tags:
      - License management
  /admin/licenses/workspaces/{workspace_id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RspDetailed'
      security:
      - BasicAuth: []
      summary: Get license by workspace
      tags:
      - License management
  /admin/users:
    get:
      consumes:
      - application/json
      parameters:
      - description: search query
        in: query
        name: search
        type: string
      - description: filter by status
        in: query
        name: status
        type: string
      - description: 'sort by field, available values: name, workspace_count, last_entrance,
          created_at'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.AdminRsp'
      security:
      - BasicAuth: []
      summary: List all users in the environment
      tags:
      - User management
  /admin/users/{user_id}/2fa:
    delete:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: delete 2FA for user
      tags:
      - User management
  /admin/users/{user_id}/status:
    put:
      consumes:
      - application/json
      parameters:
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/user.UpdateStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user status
      tags:
      - User management
  /admin/users/expire_password:
    post:
      consumes:
      - application/json
      parameters:
      - description: user login
        in: header
        name: login
        type: string
      - description: userID
        in: header
        name: user-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Make the password expired
      tags:
      - User management
  /admin/users/logout:
    post:
      consumes:
      - application/json
      parameters:
      - description: user login
        in: header
        name: login
        type: string
      - description: userID
        in: header
        name: user-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Logout specified user
      tags:
      - User management
  /admin/users/reset_password:
    post:
      consumes:
      - application/json
      parameters:
      - description: user login
        in: header
        name: login
        type: string
      - description: userID
        in: header
        name: user-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Reset password for specified user
      tags:
      - User management
  /admin/workspaces:
    get:
      consumes:
      - application/json
      parameters:
      - collectionFormat: csv
        description: 'filter by statuses: active, blocked'
        in: query
        items:
          type: string
        name: statuses
        type: array
      - description: search query
        in: query
        name: search
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/workspace.AdminRsp'
      security:
      - BasicAuth: []
      summary: List all workspaces
      tags:
      - Workspace management
  /api_key:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleAPIKey'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleAPIKey'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: API key (create API key, update API Key, block API Key, unblock API
        Key, Remove API Key)
      tags:
      - Webhook events
  /clients:
    get:
      consumes:
      - application/json
      parameters:
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/client.Row'
            type: array
      security:
      - BasicAuth: []
      summary: List all clients
      tags:
      - Clients
  /group:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleGroup'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleGroup'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Groups (create group, rename group, delete group, transfer ownership)
      tags:
      - Webhook events
  /group/user:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleGroupUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleGroupUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Group users (add user to the group, remove user from group)
      tags:
      - Webhook events
  /invite:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleInvite'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleInvite'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Invites (create invite, delete invite)
      tags:
      - Webhook events
  /licenses:
    get:
      consumes:
      - application/json
      parameters:
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: filter by statuses
        in: query
        items:
          type: string
        name: statuses
        type: array
      - collectionFormat: csv
        description: filter by payment_type (online,invoice,free)
        in: query
        items:
          type: string
        name: payment_type
        type: array
      - description: return all licenses(default false)
        in: query
        name: all
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.Rsp'
      security:
      - BasicAuth: []
      summary: List all license
      tags:
      - License
  /licenses/{license_id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RspDetailed'
      security:
      - BasicAuth: []
      summary: Get license
      tags:
      - License
  /licenses/{license_id}/attributes/buy:
    post:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: string
      - description: body
        in: body
        name: BuyAttrReq
        required: true
        schema:
          $ref: '#/definitions/license.BuyAttrReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/license.PaymentInfo'
      security:
      - BasicAuth: []
      summary: Buy attributes
      tags:
      - License
  /licenses/{license_id}/attrs/rps:
    put:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: integer
      - description: body
        in: body
        name: UpdateRPSReq
        required: true
        schema:
          $ref: '#/definitions/license.UpdateRPSReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update license rps
      tags:
      - License
  /licenses/{license_id}/cancel:
    post:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RspDetailed'
      security:
      - BasicAuth: []
      summary: Cancel license
      tags:
      - License
  /licenses/{license_id}/comment:
    put:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: integer
      - description: body
        in: body
        name: UpdateCommentReq
        required: true
        schema:
          $ref: '#/definitions/license.UpdateCommentReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RspDetailed'
      security:
      - BasicAuth: []
      summary: Update license comment
      tags:
      - License
  /licenses/{license_id}/download:
    get:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: string
      - description: format
        enum:
        - binary
        - base64
        in: query
        name: format
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Download license
      tags:
      - License
  /licenses/{license_id}/stripe/update_payment_method:
    get:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: path
        name: license_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.GetStripeLinkRsp'
      security:
      - BasicAuth: []
      summary: Update stripe payment_method
      tags:
      - License
  /licenses/create:
    get:
      consumes:
      - application/json
      parameters:
      - description: license type
        in: query
        name: type
        required: true
        type: string
      - description: license payment_type
        in: query
        name: payment_type
        required: true
        type: string
      - description: license period 1 or 12
        in: query
        name: period
        required: true
        type: integer
      - description: license price
        in: query
        name: price
        required: true
        type: number
      - description: license cancel_url
        in: query
        name: cancel_url
        type: string
      - description: license success_url
        in: query
        name: success_url
        type: string
      - description: license rps
        in: query
        name: rps
        required: true
        type: integer
      - description: license support
        in: query
        name: support
        required: true
        type: integer
      - description: license storage
        in: query
        name: storage
        required: true
        type: integer
      - description: license source
        in: query
        name: source
        required: true
        type: integer
      - description: license state_changes_per_period_m
        in: query
        name: state_changes_per_period_m
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "302":
          description: Found
      security:
      - BasicAuth: []
      summary: Create license
      tags:
      - License
  /licenses/payments:
    get:
      consumes:
      - application/json
      parameters:
      - description: license id
        in: query
        name: license_id
        type: integer
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: id, created_at, status'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: filter by statuses
        in: query
        items:
          type: string
        name: statuses
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/license.RspPayment'
            type: array
      security:
      - BasicAuth: []
      summary: List payments
      tags:
      - License
  /licenses/payments/{payment_id}/download:
    get:
      consumes:
      - application/json
      parameters:
      - description: payment id
        in: path
        name: payment_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Download invoice
      tags:
      - License
  /licenses/tariffs:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/license.CloudTariffItem'
            type: array
      security:
      - BasicAuth: []
      summary: Get tariffs
      tags:
      - License
  /licenses/user/{user_id}/status:
    put:
      consumes:
      - application/json
      parameters:
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/license.UpdateStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user status
      tags:
      - Users
  /licenses/users:
    get:
      consumes:
      - application/json
      parameters:
      - description: search query
        in: query
        name: search
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      - description: 'sort by field, available values: status'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.Users'
      security:
      - BasicAuth: []
      summary: license users list by owner
      tags:
      - License
  /login:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleLogin'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleLogin'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Logins
      tags:
      - Webhook events
  /organizations/{org_id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: organization id (support only `me` value for current user)
        in: path
        name: org_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/organization.OrgRsp'
            type: array
      security:
      - BasicAuth: []
      summary: Get organization details
      tags:
      - Organizations
  /organizations/{org_id}/domains:
    get:
      consumes:
      - application/json
      parameters:
      - description: organization id, for default organization use `user-<user_id>`
        in: path
        name: org_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/organization.DomainRsp'
            type: array
      security:
      - BasicAuth: []
      summary: List domains
      tags:
      - Organizations
    post:
      consumes:
      - application/json
      parameters:
      - description: organization id, for default organization use `user-<user_id>`
        in: path
        name: org_id
        required: true
        type: string
      - description: domain
        in: body
        name: OrgDomainReq
        required: true
        schema:
          $ref: '#/definitions/organization.OrgDomainReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Add domain to organization
      tags:
      - Organizations
  /organizations/{org_id}/domains/{domain_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: organization id, for default organization use `user-<user_id>`
        in: path
        name: org_id
        required: true
        type: string
      - description: domain
        in: path
        name: domain_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete domain from organization
      tags:
      - Organizations
  /organizations/{org_id}/idp:
    get:
      consumes:
      - application/json
      parameters:
      - description: organization id (`me` for current user, 'env' for environment)
        in: path
        name: org_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/organization.IDPRsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all identity providers
      tags:
      - Organizations
    post:
      consumes:
      - application/json
      parameters:
      - description: organization id (value `me` for current user)
        in: path
        name: org_id
        required: true
        type: string
      - description: is_test
        in: query
        name: is_test
        type: boolean
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/organization.IDPSet'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/organization.IDPRsp'
      security:
      - BasicAuth: []
      summary: Create identity provider
      tags:
      - Organizations
  /organizations/{org_id}/idp/{idp_id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: organization id (value `me` for current user)
        in: path
        name: org_id
        required: true
        type: string
      - description: identity provider id
        in: path
        name: idp_id
        required: true
        type: integer
      - description: is_test
        in: query
        name: is_test
        type: boolean
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/organization.IDPSet'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/organization.IDPRsp'
      security:
      - BasicAuth: []
      summary: Update identity provider
      tags:
      - Organizations
  /organizations/{org_id}/idp/{idp_id}/maps:
    get:
      consumes:
      - application/json
      parameters:
      - description: organization id (`me` for current user, 'env' for environment)
        in: path
        name: org_id
        required: true
        type: string
      - description: identity provider id
        in: path
        name: idp_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/organization.Map'
            type: array
      security:
      - BasicAuth: []
      summary: List map elements of the identity provider
      tags:
      - Organizations
    post:
      consumes:
      - application/json
      parameters:
      - description: organization id (`me` for current user, 'env' for environment)
        in: path
        name: org_id
        required: true
        type: string
      - description: identity provider id
        in: path
        name: idp_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/organization.CreateUpdateMap'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/organization.Map'
      security:
      - BasicAuth: []
      summary: Create map element of the identity provider
      tags:
      - Organizations
  /organizations/{org_id}/idp/{idp_id}/maps/{map_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: organization id (`me` for current user, 'env' for environment)
        in: path
        name: org_id
        required: true
        type: string
      - description: identity provider id
        in: path
        name: idp_id
        required: true
        type: integer
      - description: mapping id
        in: path
        name: map_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/organization.CreateUpdateMap'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/organization.Map'
      security:
      - BasicAuth: []
      summary: Delete map element of the identity provider.
      tags:
      - Organizations
    put:
      consumes:
      - application/json
      parameters:
      - description: organization id (`me` for current user, 'env' for environment)
        in: path
        name: org_id
        required: true
        type: string
      - description: identity provider id
        in: path
        name: idp_id
        required: true
        type: integer
      - description: mapping id
        in: path
        name: map_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/organization.CreateUpdateMap'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/organization.Map'
      security:
      - BasicAuth: []
      summary: Update map element of the identity provider
      tags:
      - Organizations
  /organizations/env/idp/{idp_id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: identity provider id
        in: path
        name: idp_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/organization.EnvIDPSet'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/organization.IDPRsp'
      security:
      - BasicAuth: []
      summary: Update identity provider in the environment
      tags:
      - Organizations
  /owner_request:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/ownerrequest.Row'
            type: array
      security:
      - BasicAuth: []
      summary: update owner request
      tags:
      - Owner Requests
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: AddReq
        required: true
        schema:
          $ref: '#/definitions/ownerrequest.AddReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: add owner request
      tags:
      - Owner Requests
  /owner_request/{id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: owner request id
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: UpdateReq
        required: true
        schema:
          $ref: '#/definitions/ownerrequest.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: update owner request
      tags:
      - Owner Requests
  /perms:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/perm.Rsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all perms
      tags:
      - Permissions
  /role:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleRole'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleRole'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Roles (create custom role, rename custom role, delete custom role)
      tags:
      - Webhook events
  /role/user:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleRoleUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleRoleUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Role users (add user to the role, remove user from role)
      tags:
      - Webhook events
  /scopes:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/scope.Rsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all scopes
      tags:
      - Scopes
  /state_changes:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleStateChanges'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleStateChanges'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: State Changes
      tags:
      - Webhook events
  /user:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/swagger.ExampleUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Users (Add user to workspace, remove user from workspace, update User
        status)
      tags:
      - Webhook events
  /users/{user_id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: user_id, 'me or 0' for own data
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.UserRow'
      security:
      - BasicAuth: []
      summary: Get user's data
      tags:
      - Users
    put:
      consumes:
      - application/json
      parameters:
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/user.Update'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user
      tags:
      - Users
  /users/{user_id}/tokens:
    get:
      consumes:
      - application/json
      parameters:
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.UserTokenListRsp'
      security:
      - BasicAuth: []
      summary: List user tokens
      tags:
      - Users
    post:
      consumes:
      - application/json
      parameters:
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/user.UserTokenCreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/user.UserTokenRsp'
      security:
      - BasicAuth: []
      summary: Create user token
      tags:
      - Users
  /users/{user_id}/tokens/{token_id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: token_id
        in: path
        name: token_id
        required: true
        type: string
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/user.UpdateUserTokenReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.UserTokenRsp'
      security:
      - BasicAuth: []
      summary: Update user token
      tags:
      - Users
  /users/check:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.CheckRow'
      security:
      - BasicAuth: []
      summary: Get user's own data
      tags:
      - Users
  /users/company_info:
    put:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: UpdateCompanyInfoReq
        required: true
        schema:
          $ref: '#/definitions/user.UpdateCompanyInfoReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: update company info
      tags:
      - Users
  /version:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/http.VersionRsp'
      summary: Get version of API
      tags:
      - Version
  /workspaces:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: organization id (value `me` for current user)
        in: query
        name: org_id
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/workspace.ShortRsp'
      security:
      - BasicAuth: []
      summary: List all workspaces
      tags:
      - Workspaces
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/workspace.Req'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/workspace.Rsp'
      security:
      - BasicAuth: []
      summary: Create workspace
      tags:
      - Workspaces
  /workspaces/{workspace_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete workspace
      tags:
      - Workspaces
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/workspace.Rsp'
      security:
      - BasicAuth: []
      summary: Get workspace details
      tags:
      - Workspaces
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: ReqUpdate
        required: true
        schema:
          $ref: '#/definitions/workspace.ReqUpdate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/workspace.Rsp'
      security:
      - BasicAuth: []
      summary: Update workspace (name or/and status)
      tags:
      - Workspaces
  /workspaces/{workspace_id}/apis:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.Row'
            type: array
      security:
      - BasicAuth: []
      summary: List all api users
      tags:
      - API Users
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/api.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/api.Row'
      security:
      - BasicAuth: []
      summary: Create api user
      tags:
      - API Users
  /workspaces/{workspace_id}/apis/{api_user_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: api_user_id
        in: path
        name: api_user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Remove api user
      tags:
      - API Users
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: api_user_id
        in: path
        name: api_user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/api.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update api user info
      tags:
      - API Users
  /workspaces/{workspace_id}/groups:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: member userID to filter by
        in: query
        name: member_user_id
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/group.Rsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all groups
      tags:
      - Groups
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/group.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/group.Rsp'
      security:
      - BasicAuth: []
      summary: Create group
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete group
      tags:
      - Groups
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/group.Rsp'
      security:
      - BasicAuth: []
      summary: Get group info
      tags:
      - Groups
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: body
        in: body
        name: UpdateReq
        required: true
        schema:
          $ref: '#/definitions/group.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/group.Rsp'
      security:
      - BasicAuth: []
      summary: Update the group
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}/apis:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/group.RowAPIUser'
            type: array
      security:
      - BasicAuth: []
      summary: Get group api users
      tags:
      - Groups
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: body
        in: body
        name: '[]UpdateUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/group.UpdateUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update api user list
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}/owner:
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: body
        in: body
        name: ChangeOwner
        required: true
        schema:
          $ref: '#/definitions/group.ChangeOwner'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Change group owner
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}/users:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: search query
        in: query
        name: search
        type: string
      - collectionFormat: csv
        description: filter by roles
        in: query
        items:
          type: string
        name: roles
        type: array
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/group.RowUser'
            type: array
      security:
      - BasicAuth: []
      summary: Get group users
      tags:
      - Groups
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: body
        in: body
        name: '[]UpdateUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/group.UpdateUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user list
      tags:
      - Groups
  /workspaces/{workspace_id}/invites:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - collectionFormat: csv
        description: filter by role ids
        in: query
        items:
          type: integer
        name: roles
        type: array
      - description: 'sort by field, available values: login'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/invite.Rsp'
      security:
      - BasicAuth: []
      summary: List all invites
      tags:
      - Invites
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/invite.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/invite.RspItem'
      security:
      - BasicAuth: []
      summary: Create invite
      tags:
      - Invites
  /workspaces/{workspace_id}/invites/{invite_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: invite_id
        in: path
        name: invite_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Remove from the workspace
      tags:
      - Invites
  /workspaces/{workspace_id}/roles:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/role.Rsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all roles
      tags:
      - Roles
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/role.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/role.Rsp'
      security:
      - BasicAuth: []
      summary: Create role
      tags:
      - Roles
  /workspaces/{workspace_id}/roles/{role_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: role id
        in: path
        name: role_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete role
      tags:
      - Roles
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: role id
        in: path
        name: role_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/role.Rsp'
      security:
      - BasicAuth: []
      summary: Get role info
      tags:
      - Roles
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: role id
        in: path
        name: role_id
        required: true
        type: integer
      - description: body
        in: body
        name: UpdateReq
        required: true
        schema:
          $ref: '#/definitions/role.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/role.Rsp'
      security:
      - BasicAuth: []
      summary: Update role
      tags:
      - Roles
  /workspaces/{workspace_id}/roles/{role_id}/perms:
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: role id
        in: path
        name: role_id
        required: true
        type: integer
      - description: body
        in: body
        name: '[]UpdateRolePermName'
        required: true
        schema:
          items:
            $ref: '#/definitions/role.UpdateRolePermName'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/role.Rsp'
      security:
      - BasicAuth: []
      summary: Update the role permissions
      tags:
      - Roles
  /workspaces/{workspace_id}/roles/{role_id}/users:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: role id
        in: path
        name: role_id
        required: true
        type: integer
      - description: search query
        in: query
        name: search
        type: string
      - collectionFormat: csv
        description: filter by roles
        in: query
        items:
          type: string
        name: roles
        type: array
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/role.RowUser'
            type: array
      security:
      - BasicAuth: []
      summary: Get role users
      tags:
      - Roles
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: role id
        in: path
        name: role_id
        required: true
        type: integer
      - description: body
        in: body
        name: '[]UpdateUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/role.UpdateUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user list
      tags:
      - Roles
  /workspaces/{workspace_id}/roles/available:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/role.AvailableRsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all available roles
      tags:
      - Roles
  /workspaces/{workspace_id}/roles/change_owner:
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: ChangeOwnerReq
        required: true
        schema:
          $ref: '#/definitions/role.ChangeOwnerReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Change owner of the workspace
      tags:
      - Roles
  /workspaces/{workspace_id}/roles/perms:
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: '[]UpdatePermName'
        required: true
        schema:
          items:
            $ref: '#/definitions/role.UpdatePermName'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update permissions
      tags:
      - Roles
  /workspaces/{workspace_id}/users:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - collectionFormat: csv
        description: filter by role ids
        in: query
        items:
          type: integer
        name: roles
        type: array
      - description: rsp with groups,created_at fields
        in: query
        name: with
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.Rsp'
      security:
      - BasicAuth: []
      summary: List all users
      tags:
      - Users
  /workspaces/{workspace_id}/users/{user_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: new_owner_id
        in: query
        name: new_owner_id
        type: integer
      - collectionFormat: csv
        description: obj_owner_ids
        in: query
        items:
          type: integer
        name: obj_owner_ids
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Remove from the workspace
      tags:
      - Users
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user id
        in: path
        name: user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.RspItem'
      security:
      - BasicAuth: []
      summary: Get user info
      tags:
      - Users
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/user.UpdateStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user status
      tags:
      - Users
  /workspaces/{workspace_id}/users/{user_id}/groups:
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: '[]UpdateGroup'
        required: true
        schema:
          items:
            $ref: '#/definitions/user.UpdateGroup'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: add/remove user to/from the group
      tags:
      - Users
  /workspaces/{workspace_id}/users/{user_id}/roles:
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: '[]UpdateRole'
        required: true
        schema:
          items:
            $ref: '#/definitions/user.UpdateRole'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: add/remove user to/from the role
      tags:
      - Users
  /workspaces/{workspace_id}/users/search/{query}:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: query
        in: path
        name: query
        required: true
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/user.SearchRow'
            type: array
      security:
      - BasicAuth: []
      summary: Search users by id/name/login
      tags:
      - Users
  /workspaces/{workspace_id}/webhooks:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/webhook.Rsp'
      security:
      - BasicAuth: []
      summary: List all webhooks
      tags:
      - Webhooks
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/webhook.CreateRow'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/webhook.RspItem'
      security:
      - BasicAuth: []
      summary: Create webhook
      tags:
      - Webhooks
  /workspaces/{workspace_id}/webhooks/{id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: webhook id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete webhook
      tags:
      - Webhooks
    put:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: webhook id
        in: path
        name: id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/webhook.UpdateRow'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/webhook.RspItem'
      security:
      - BasicAuth: []
      summary: Update webhook
      tags:
      - Webhooks
securityDefinitions:
  BasicAuth:
    type: basic
swagger: "2.0"
