package workspace

import "time"

type CreateReqUsers struct {
	RoleID int `json:"role_id"  default:"1"`
	UserID int `json:"user_id"  default:"45317"`
}

type CreateReqAPI struct {
	UserID  int    `json:"user_id"  default:"45317"`
	OwnerID int    `json:"owner_id"  default:"45317"`
	URL     string `json:"url" default:"http://localhost:8080"`
}

type CreateReq struct {
	Name        string           `json:"name" default:"My workspace"`
	ExtID       *string          `json:"ext_id,omitempty"  default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	Attributes  struct{}         `json:"attributes"`
	Users       []CreateReqUsers `json:"users"`
	APIs        []CreateReqAPI   `json:"apis"`
	SkipOnExist bool             `json:"skip_on_exist"`
}

type Row struct {
	ExtID    string `json:"ext_id" default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	Name     string `json:"name" default:"My workspace"`
	IsActive bool   `json:"active" default:"true"`
}

type UserRow struct {
	ID     int    `json:"id" default:"1"`
	Name   string `json:"name" default:"My workspace"`
	Active bool   `json:"active" default:"true"`
}

type CreateRsp struct {
	ExtID string `json:"ext_id"  default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
}

type OwnerRow struct {
	ID    int     `json:"id" default:"1"`
	Name  string  `json:"name" default:"Ivan Ivanov"`
	Photo *string `json:"photo,omitempty" default:"https://account.corezoid.com/avatars/0.jpg"`
	Color *string `json:"color,omitempty" default:"#000000"`
}

type AISetting struct {
	Provider string `json:"provider,omitempty"`
	APIKey   string `json:"api_key,omitempty"`
}

type Settings struct {
	DisableInvites    *bool       `json:"disable_invites,omitempty"`
	AllowedDomains    *[]string   `json:"allowed_domains,omitempty"`
	ForbiddenDomains  *[]string   `json:"forbidden_domains,omitempty"`
	SimClient         *string     `json:"sim_client,omitempty"`
	AutoRecording     *bool       `json:"auto_recording,omitempty"`
	TranscriptionLang *string     `json:"transcription_lang,omitempty"`
	FileTTL           *int        `json:"file_ttl,omitempty"`
	AISettings        []AISetting `json:"ai_settings,omitempty"`
}

type Workspace struct {
	ID        int        `json:"id" default:"1"`
	ExtID     *string    `json:"ext_id" default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	Name      string     `json:"name" default:"My workspace"`
	Photo     *string    `json:"photo,omitempty"`
	Color     *string    `json:"color,omitempty"`
	Status    string     `json:"status" default:"active"`
	CreatedAt time.Time  `json:"created_at"  default:"2022-10-10 09-09-09"`
	Owners    []OwnerRow `json:"owners"`
	Settings  Settings   `json:"settings"`
}
