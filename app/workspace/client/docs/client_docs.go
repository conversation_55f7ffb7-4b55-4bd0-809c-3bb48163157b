// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplateclient = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/apis": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Create api user",
                "parameters": [
                    {
                        "description": "body",
                        "name": "CreateReqNoRightsCheking",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.CreateReqNoRightsCheking"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/api.Rsp"
                        }
                    }
                }
            }
        },
        "/apis/me": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Get api details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.Rsp"
                        }
                    }
                }
            }
        },
        "/apis/scopes/{type}/{scopes}/verify": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Verify scopes",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA api token",
                        "name": "sa-api-token",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "scope type (control/corezoid)",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "scopes (comma separated)",
                        "name": "scopes",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.Rsp"
                        }
                    }
                }
            }
        },
        "/clients/credentials": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Client"
                ],
                "summary": "Get environment credentials(public_key and private_key)",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/client.CredentialsRsp"
                        }
                    }
                }
            }
        },
        "/clients/subscribe": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Client"
                ],
                "summary": "Update client info",
                "parameters": [
                    {
                        "description": "body",
                        "name": "UpdateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/client.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/desktops/{uuid}/sync": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Client"
                ],
                "summary": "Sync desktop state",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "desktop uuid",
                        "name": "uuid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "boolean",
                        "description": "flag to get license file",
                        "name": "license",
                        "in": "query"
                    },
                    {
                        "description": "body",
                        "name": "SyncReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/desktop.SyncReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/invite/add": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Add invite",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleInvite",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleInvite"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/invite/confirm": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Confirm invite",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleInvite",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleInvite"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/invite/del": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Delete invite",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleInvite",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleInvite"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/license/attrs/update": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "License attr update",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleLicense",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleLicense"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/licenses": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "List all license",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by statuses",
                        "name": "statuses",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/license.RspItem"
                            }
                        }
                    }
                }
            }
        },
        "/licenses/pub_keys/env": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Link the license with the env_id via pub_key",
                "parameters": [
                    {
                        "description": "body",
                        "name": "UpdateEnvReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/license.UpdateEnvReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/licenses/root": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Return root license info",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RootRsp"
                        }
                    }
                }
            }
        },
        "/licenses/state_changes/used": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License management"
                ],
                "summary": "Set used state changes",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]SetStateChangesReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/license.SetStateChangesReq"
                            }
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/licenses/users/with_no_state_changes": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Get users with no state changes",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.UsersWithNoStateChanges"
                        }
                    }
                }
            }
        },
        "/licenses/users/{user_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Get license by user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RspDetailed"
                        }
                    }
                }
            }
        },
        "/licenses/workspaces/{workspace_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License"
                ],
                "summary": "Get license by workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/license.RspDetailed"
                        }
                    }
                }
            }
        },
        "/perms/{type}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Perms"
                ],
                "summary": "List all perms by type",
                "parameters": [
                    {
                        "type": "string",
                        "description": "perm type",
                        "name": "type",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/perm.Rsp"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Perms"
                ],
                "summary": "Create perms and assign to roles",
                "parameters": [
                    {
                        "type": "string",
                        "description": "perm type (control/corezoid)",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/perm.CreateReq"
                            }
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/perms/{type}/{ext_id}": {
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Perms"
                ],
                "summary": "Delete perm",
                "parameters": [
                    {
                        "type": "string",
                        "description": "perm type",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "perm ext_id",
                        "name": "ext_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/scopes/{type}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scopes"
                ],
                "summary": "List all scopes by type",
                "parameters": [
                    {
                        "type": "string",
                        "description": "scope type (control/corezoid)",
                        "name": "type",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/scope.Rsp"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scopes"
                ],
                "summary": "Create scope and assign to roles",
                "parameters": [
                    {
                        "type": "string",
                        "description": "scope type (control/corezoid)",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/scope.CreateReq"
                            }
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/scopes/{type}/{ext_id}": {
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scopes"
                ],
                "summary": "Delete scope",
                "parameters": [
                    {
                        "type": "string",
                        "description": "scope type (control/corezoid)",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "scope ext_id",
                        "name": "ext_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/user/login": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "User login",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleUserLogin",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleUserLogin"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/user/logout": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "User logout",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleUserLogout",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleUserLogout"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/user/set": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Update user",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleUserSet",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleUserSet"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/users": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Create user",
                "parameters": [
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/user.CreateUserRsp"
                        }
                    }
                }
            }
        },
        "/users/array": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Create users",
                "parameters": [
                    {
                        "description": "body",
                        "name": "CreateArrayReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.CreateArrayReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.CreateUserRsp"
                            }
                        }
                    }
                }
            }
        },
        "/users/me": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get user details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user login",
                        "name": "sa-login",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.Rsp"
                        }
                    }
                }
            }
        },
        "/users/me/short": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get user details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user login",
                        "name": "sa-login",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.RspShort"
                        }
                    }
                }
            }
        },
        "/users/me/workspaces": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Get user workspaces where he is the owner",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user login",
                        "name": "sa-login",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.RspWorkspaceItem"
                            }
                        }
                    }
                }
            }
        },
        "/users/online": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Set the status of user sessions to \"online\"",
                "parameters": [
                    {
                        "description": "body",
                        "name": "OnlineReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.OnlineReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/users/sso": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Authorize user via SSO (and create if needed)",
                "parameters": [
                    {
                        "description": "body",
                        "name": "SSOReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.SSOReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.RspSSO"
                        }
                    }
                }
            }
        },
        "/users/{user_id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user status",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UpdateStatusReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/add": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Create workspace",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSAdd",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSAdd"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/api_user/set": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Update api user info",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSAPIUserSet",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSAPIUserSet"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/del": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Delete workspace",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSDel",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSDel"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/group/add": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Create new group",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSGroupAdd",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSGroupAdd"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/group/del": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Delete group",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSGroupDel",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSGroupDel"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/group/set": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Change group info",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSGroupSet",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSGroupSet"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/group/user/set": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Change group member list",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSGroupUserSet",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSGroupUserSet"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/set": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Update workspace",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSSet",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSSet"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/sync": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Sync workspace",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSSync",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSSync"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/user/perm/set": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Change user permission list",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSUserPerm",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSUserPerm"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspace/user/set": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Webhook"
                ],
                "summary": "Change member list in the workspace",
                "parameters": [
                    {
                        "description": "body",
                        "name": "[]ExampleWSUserSet",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/swaggwebhook.ExampleWSUserSet"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "List all workspaces",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/workspace.ShortRsp"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Create workspace",
                "parameters": [
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/workspace.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/workspace.CreateRsp"
                        }
                    }
                }
            }
        },
        "/workspaces/search/{query}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Search workspaces by ext_id/name/",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "query",
                        "name": "query",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "limit",
                        "name": "limit",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/workspace.Row"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/sync": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Search",
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Get workspace details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace_id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/workspace.Workspace"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Workspaces"
                ],
                "summary": "Delete workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/apis": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "List all api users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/api.Row"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Create api user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/api.Row"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/apis/{api_user_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Get api user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "api_user_id",
                        "name": "api_user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.Row"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Remove api user",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "api_user_id",
                        "name": "api_user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API Users"
                ],
                "summary": "Partial update of API user information",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "api_user_id",
                        "name": "api_user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "PatchReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.PatchReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "List all groups",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.Rsp"
                            }
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Create group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/group.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/group.Rsp"
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Get group info",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/group.Rsp"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Update the group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "UpdateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/group.UpdateReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/group.Rsp"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Delete group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}/apis": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Get group api users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.RowAPIUser"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}/users": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Get group users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.RowUser"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}/users/check": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Check users in the group",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "integer"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by list of user id",
                        "name": "ids",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "integer"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/groups/{group_id}/users2apis": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Groups"
                ],
                "summary": "Update user list",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "group id",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/group.UpdateUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/invites": {
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Invites"
                ],
                "summary": "Create invite",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "CreateReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/invite.CreateReq"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/invites/{login}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Invites"
                ],
                "summary": "Get invite",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "login",
                        "name": "login",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/invite.Row"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Invites"
                ],
                "summary": "Remove from the workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "login",
                        "name": "login",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/roles/available": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "List all available roles",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "search query",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "sort by field, available values: name",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "order by asc or desc",
                        "name": "order_by",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/role.AvailableRsp"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "List workspace users",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "SA user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "integer",
                        "description": "limit for lazy loading",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "offset for lazy loading",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.ShortRow"
                            }
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Change user list in the workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]UpdateUser",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.UpdateUser"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Add user to the workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "[]AddReq",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.AddReq"
                            }
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/check": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Check users in the workspace",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "integer"
                        },
                        "collectionFormat": "csv",
                        "description": "filter by list of user id",
                        "name": "ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "integer"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/search/{query}": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Search users by id/name/login",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "query",
                        "name": "query",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "limit",
                        "name": "limit",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "offset",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/user.ShortRow"
                            }
                        }
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/{user_id}": {
            "put": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Update user status",
                "parameters": [
                    {
                        "type": "string",
                        "description": "user id",
                        "name": "sa-user-id",
                        "in": "header"
                    },
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "user_id",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "body",
                        "name": "Req",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/user.UpdateStatusReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    }
                }
            }
        },
        "/workspaces/{workspace_id}/users/{user_id}/perms/{type}/{perms}/verify": {
            "get": {
                "security": [
                    {
                        "BasicAuth": []
                    }
                ],
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Verify permissions",
                "parameters": [
                    {
                        "type": "string",
                        "description": "workspace id",
                        "name": "workspace_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "SA userID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "scope type (sa/control)",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "perms (comma separated)",
                        "name": "perms",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/user.VerifyRsp"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "api.CreateReq": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.Scope"
                    }
                },
                "secret": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "api.CreateReqNoRightsCheking": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.Scope"
                    }
                },
                "url": {
                    "type": "string"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "afdasfsad"
                }
            }
        },
        "api.PatchReq": {
            "type": "object",
            "properties": {
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                }
            }
        },
        "api.Row": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string"
                },
                "owner_id": {
                    "type": "integer"
                },
                "owner_name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.Scope"
                    }
                },
                "status": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "api.Rsp": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.RspGroup"
                    }
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "nick": {
                    "type": "string",
                    "default": "Process 123"
                },
                "owner_id": {
                    "type": "integer",
                    "default": 1
                },
                "photo": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                },
                "status": {
                    "description": "API User status, values: active/blocked",
                    "type": "string",
                    "default": "active"
                },
                "url": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "1"
                },
                "workspace_name": {
                    "type": "string"
                }
            }
        },
        "api.RspGroup": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "is_member": {
                    "type": "boolean",
                    "default": true
                },
                "is_owner": {
                    "type": "boolean",
                    "default": true
                },
                "name": {
                    "type": "string",
                    "default": "My group"
                }
            }
        },
        "api.Scope": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "default": "actors:read_only"
                },
                "type": {
                    "type": "string",
                    "default": "scopes.control"
                }
            }
        },
        "client.CredentialsRsp": {
            "type": "object",
            "properties": {
                "algorithm": {
                    "type": "string"
                },
                "private_key": {
                    "type": "string"
                },
                "public_key": {
                    "type": "string"
                }
            }
        },
        "client.UpdateReq": {
            "type": "object",
            "properties": {
                "notify_url": {
                    "type": "string"
                },
                "subscribe_to": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "all"
                    ]
                }
            }
        },
        "desktop.SyncReq": {
            "type": "object",
            "required": [
                "access_token"
            ],
            "properties": {
                "access_token": {
                    "type": "string"
                }
            }
        },
        "group.CreateReq": {
            "type": "object",
            "properties": {
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string"
                },
                "users": {
                    "description": "list of users and api users",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "group.OwnerRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                }
            }
        },
        "group.RowAPIUser": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                }
            }
        },
        "group.RowUser": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/group.RspLogin"
                    }
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string"
                }
            }
        },
        "group.Rsp": {
            "type": "object",
            "properties": {
                "api_count": {
                    "type": "integer",
                    "default": 1
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string",
                    "default": "Default"
                },
                "owners": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/group.OwnerRow"
                    }
                },
                "user_count": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "group.RspLogin": {
            "type": "object",
            "properties": {
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "type": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "group.UpdateReq": {
            "type": "object",
            "properties": {
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string"
                },
                "owner_id": {
                    "type": "integer"
                }
            }
        },
        "group.UpdateUser": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "id": {
                    "description": "ID is user_id or api user_id",
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "invite.CreateReq": {
            "type": "object",
            "properties": {
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "redirect_uri": {
                    "type": "string",
                    "default": "https://admin.corezoid.com"
                },
                "role_id": {
                    "type": "integer",
                    "default": 1
                },
                "type": {
                    "description": "available types: api, google, facebook, ldap, oauth_pb, phone, corezoid, github, apple, keycloak",
                    "type": "string",
                    "default": "email"
                }
            }
        },
        "invite.Row": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "redirect_uri": {
                    "type": "string"
                },
                "role_id": {
                    "type": "integer"
                },
                "type": {
                    "type": "integer"
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "license.AttributeData": {
            "type": "object",
            "properties": {
                "value": {
                    "type": "integer"
                }
            }
        },
        "license.Attributes": {
            "type": "object",
            "properties": {
                "rps": {
                    "$ref": "#/definitions/license.AttributeData"
                },
                "state_changes": {
                    "$ref": "#/definitions/license.AttributeData"
                },
                "storage": {
                    "$ref": "#/definitions/license.AttributeData"
                },
                "support": {
                    "$ref": "#/definitions/license.AttributeData"
                },
                "users": {
                    "$ref": "#/definitions/license.AttributeData"
                },
                "white_label": {
                    "$ref": "#/definitions/license.AttributeData"
                }
            }
        },
        "license.InvoiceInfo": {
            "type": "object",
            "properties": {
                "file_url": {
                    "type": "string"
                }
            }
        },
        "license.PaymentInfo": {
            "type": "object",
            "properties": {
                "invoice": {
                    "$ref": "#/definitions/license.InvoiceInfo"
                }
            }
        },
        "license.RootRsp": {
            "type": "object",
            "properties": {
                "account_license_module": {
                    "type": "boolean"
                },
                "cluster_id": {
                    "type": "string"
                },
                "company_id": {
                    "type": "string"
                },
                "created_time": {
                    "type": "string"
                },
                "is_allow": {
                    "type": "boolean"
                },
                "issuer": {
                    "type": "string"
                },
                "max_active_procs": {
                    "type": "integer"
                },
                "max_rpm": {
                    "type": "integer"
                },
                "max_rps": {
                    "type": "integer"
                },
                "max_scpm": {
                    "type": "integer"
                },
                "max_storage_size": {
                    "type": "integer"
                },
                "max_task_size_process": {
                    "type": "integer"
                },
                "max_task_size_state_diagram": {
                    "type": "integer"
                },
                "max_users": {
                    "type": "integer"
                },
                "min_timer": {
                    "type": "integer"
                },
                "multi_tenancy": {
                    "type": "boolean"
                },
                "pub_key": {
                    "type": "string"
                },
                "time_to_expire": {
                    "type": "integer"
                },
                "time_to_start": {
                    "type": "integer"
                }
            }
        },
        "license.RspDetailed": {
            "type": "object",
            "properties": {
                "attributes": {
                    "$ref": "#/definitions/license.Attributes"
                },
                "comment": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "multitenant": {
                    "type": "boolean"
                },
                "next_payment_at": {
                    "type": "string"
                },
                "payment": {
                    "$ref": "#/definitions/license.PaymentInfo"
                },
                "payment_type": {
                    "description": "available values: invoice, stripe",
                    "type": "string",
                    "enum": [
                        "invoice",
                        "stripe"
                    ]
                },
                "period": {
                    "description": "available values: 1, 12",
                    "type": "integer"
                },
                "price": {
                    "type": "number"
                },
                "reason": {
                    "type": "string"
                },
                "status": {
                    "description": "available values: active, pending, expired, canceled",
                    "type": "string",
                    "enum": [
                        "active",
                        "pending",
                        " expired",
                        "canceled"
                    ]
                },
                "type": {
                    "description": "available values: cloud, in_house, desktop",
                    "type": "string",
                    "enum": [
                        "cloud",
                        "in_house",
                        "desktop"
                    ]
                },
                "workspaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/license.WorkspaceInfo"
                    }
                }
            }
        },
        "license.RspItem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "invoice": {
                    "$ref": "#/definitions/license.InvoiceInfo"
                },
                "next_payment_at": {
                    "type": "string"
                },
                "payment_type": {
                    "type": "string",
                    "enum": [
                        "invoice",
                        "stripe"
                    ]
                },
                "period": {
                    "type": "integer"
                },
                "price": {
                    "type": "number"
                },
                "reason": {
                    "type": "string"
                },
                "status": {
                    "description": "available values: active, pending, expired, canceled",
                    "type": "string",
                    "enum": [
                        "active",
                        "pending",
                        " expired",
                        "canceled"
                    ]
                },
                "type": {
                    "description": "available values: cloud, in_house, desktop",
                    "type": "string",
                    "enum": [
                        "cloud",
                        "in_house",
                        "desktop"
                    ]
                }
            }
        },
        "license.SetStateChangesReq": {
            "type": "object",
            "properties": {
                "user_id": {
                    "type": "integer"
                },
                "value": {
                    "type": "integer"
                },
                "workspace_id": {
                    "type": "string"
                }
            }
        },
        "license.UpdateEnvReq": {
            "type": "object",
            "properties": {
                "env": {
                    "type": "string"
                },
                "pub_key": {
                    "type": "string"
                }
            }
        },
        "license.UsersWithNoStateChanges": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "license.WorkspaceInfo": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string"
                }
            }
        },
        "perm.CreateReq": {
            "type": "object",
            "properties": {
                "active_in_roles": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "ext_id": {
                    "type": "string",
                    "default": "actors"
                },
                "name": {
                    "type": "string",
                    "default": "actors"
                }
            }
        },
        "perm.Rsp": {
            "type": "object",
            "properties": {
                "active_in_roles": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "ext_id": {
                    "type": "string",
                    "default": "actors"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "actors"
                }
            }
        },
        "role.AvailableRsp": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Default"
                }
            }
        },
        "scope.CreateReq": {
            "type": "object",
            "properties": {
                "ext_id": {
                    "type": "string",
                    "default": "users.readonly"
                },
                "name": {
                    "type": "string",
                    "default": "Read only account users information"
                }
            }
        },
        "scope.Rsp": {
            "type": "object",
            "properties": {
                "ext_id": {
                    "type": "string",
                    "default": "actors"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "actors"
                }
            }
        },
        "swaggwebhook.AISetting": {
            "type": "object",
            "properties": {
                "api_key": {
                    "type": "string"
                },
                "provider": {
                    "type": "string"
                }
            }
        },
        "swaggwebhook.ExampleInvite": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.Invite"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "invite/del"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleLicense": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.License"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "license/attrs/update"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleUserLogin": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.UserLogin"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "user/login"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleUserLogout": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.UserLogout"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "user/logout"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleUserSet": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.UserSet"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "user/set"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSAPIUserSet": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSAPIUserSet"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/api_user/set"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSAdd": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSAdd"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/add"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSDel": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSDel"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/del"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSGroupAdd": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSGroupAdd"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/group/add"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSGroupDel": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSGroupDel"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/group/del"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSGroupSet": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSGroupSet"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/group/set"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSGroupUserSet": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSGroupUserSet"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/group/user/set"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSSet": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSSet"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/set"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSSync": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSAdd"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/sync"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSUserPerm": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSUserPerm"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/user/perm/set"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.ExampleWSUserSet": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "default": "2023-01-28T21:19:42"
                },
                "data": {
                    "$ref": "#/definitions/swaggwebhook.WSUserSet"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "workspace/user/set"
                },
                "workspace_id": {
                    "type": "string",
                    "default": "beb78608-4659-4529-bfa6-905975563f21"
                }
            }
        },
        "swaggwebhook.Invite": {
            "type": "object",
            "properties": {
                "client_id": {
                    "type": "string"
                },
                "client_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "login": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "swaggwebhook.License": {
            "type": "object",
            "properties": {
                "attributes": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "user_id": {
                    "type": "integer"
                },
                "workspaces": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "swaggwebhook.Settings": {
            "type": "object",
            "properties": {
                "ai_settings": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.AISetting"
                    }
                },
                "allowed_domains": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "auto_recording": {
                    "type": "boolean"
                },
                "disable_invites": {
                    "type": "boolean"
                },
                "file_ttl": {
                    "type": "integer"
                },
                "forbidden_domains": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "sim_client": {
                    "type": "string"
                },
                "transcription_lang": {
                    "type": "string"
                }
            }
        },
        "swaggwebhook.UserLogin": {
            "type": "object",
            "properties": {
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "swaggwebhook.UserLogout": {
            "type": "object",
            "properties": {
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "swaggwebhook.UserSet": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "https://color.com"
                },
                "id": {
                    "type": "integer"
                },
                "lang": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string",
                    "default": "https://photo.com"
                },
                "status": {
                    "type": "string",
                    "default": "active",
                    "enum": [
                        "active",
                        "blocked_by_license"
                    ]
                }
            }
        },
        "swaggwebhook.WSAPIUserSet": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan"
                },
                "photo": {
                    "type": "string",
                    "default": "https://google.com"
                },
                "scopes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.WSAPIUserSetItem"
                    }
                },
                "url": {
                    "type": "string",
                    "default": "https://google.com"
                }
            }
        },
        "swaggwebhook.WSAPIUserSetItem": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "swaggwebhook.WSAdd": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "https://color.com"
                },
                "name": {
                    "type": "string",
                    "default": "My group"
                },
                "owner_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "photo": {
                    "type": "string",
                    "default": "https://photo.com"
                }
            }
        },
        "swaggwebhook.WSDel": {
            "type": "object",
            "properties": {
                "author_id": {
                    "type": "integer"
                }
            }
        },
        "swaggwebhook.WSGroupAdd": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string",
                    "default": "My group name"
                },
                "owner_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.WSGroupAddItem"
                    }
                }
            }
        },
        "swaggwebhook.WSGroupAddItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "swaggwebhook.WSGroupDel": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string",
                    "default": "My group"
                },
                "owner_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "swaggwebhook.WSGroupSet": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "meta": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "name": {
                    "type": "string",
                    "default": "My group"
                },
                "owner_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "swaggwebhook.WSGroupUserSet": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.WSGroupUserSetItem"
                    }
                }
            }
        },
        "swaggwebhook.WSGroupUserSetItem": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "swaggwebhook.WSSet": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "https://color.com"
                },
                "name": {
                    "type": "string",
                    "default": "My group"
                },
                "owner_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "photo": {
                    "type": "string",
                    "default": "https://photo.com"
                },
                "settings": {
                    "$ref": "#/definitions/swaggwebhook.Settings"
                }
            }
        },
        "swaggwebhook.WSUserPerm": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.WSUserPermItem"
                    }
                }
            }
        },
        "swaggwebhook.WSUserPermItem": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "type": {
                    "type": "string",
                    "default": "control"
                }
            }
        },
        "swaggwebhook.WSUserSet": {
            "type": "object",
            "properties": {
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.WSUserSetItem"
                    }
                }
            }
        },
        "swaggwebhook.WSUserSetItem": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "deprecated",
                    "type": "boolean"
                },
                "author_id": {
                    "description": "id of user who changed status",
                    "type": "integer"
                },
                "color": {
                    "type": "string",
                    "default": "https://color.com"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "obj_owner_ids": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.WSUserSetItemObjOwnerID"
                    }
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/swaggwebhook.WSUserPermItem"
                    }
                },
                "photo": {
                    "type": "string",
                    "default": "https://photo.com"
                },
                "status": {
                    "description": "status: added, blocked, active,deleted",
                    "type": "string"
                }
            }
        },
        "swaggwebhook.WSUserSetItemObjOwnerID": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                }
            }
        },
        "user.AddReq": {
            "type": "object",
            "properties": {
                "role_id": {
                    "description": "example: 1",
                    "type": "integer"
                },
                "user_id": {
                    "description": "example: 1",
                    "type": "integer"
                }
            }
        },
        "user.CreateArrayReq": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "hash": {
                                "type": "string",
                                "default": "123"
                            },
                            "login": {
                                "type": "string",
                                "default": "<EMAIL>"
                            },
                            "login_type": {
                                "description": "available: api, google, facebook, ldap, oauth, phone, corezoid, github, apple, keycloak, sso, microsoft",
                                "type": "string",
                                "default": "google"
                            },
                            "nick": {
                                "type": "string",
                                "default": "Ivan Ivanov"
                            }
                        }
                    }
                }
            }
        },
        "user.CreateReq": {
            "type": "object",
            "properties": {
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "login_type": {
                    "description": "available: google, facebook, ldap, oauth_pb, phone, github, apple, microsoft",
                    "type": "string",
                    "default": "google"
                },
                "nick": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                },
                "skip_on_exist": {
                    "type": "boolean"
                }
            }
        },
        "user.CreateUserRsp": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "nick": {
                    "type": "string"
                }
            }
        },
        "user.OnlineReq": {
            "type": "object",
            "properties": {
                "users": {
                    "description": "Users is the list of user ids (max size = 1000)",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "user.PermName": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "default": "actorsBag"
                },
                "type": {
                    "type": "string",
                    "default": "perms.control"
                }
            }
        },
        "user.RoleRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "my role"
                }
            }
        },
        "user.Rsp": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspLogin"
                    }
                },
                "nick": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                },
                "options": {
                    "type": "object",
                    "properties": {
                        "disable_workspace_creation": {
                            "type": "boolean"
                        }
                    }
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.PermName"
                    }
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.ShortRowPermItem"
                    }
                },
                "photo": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspWorkspaceRole"
                    }
                },
                "tokens": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.TokenRsp"
                    }
                },
                "workspaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspWorkspace"
                    }
                }
            }
        },
        "user.RspLogin": {
            "type": "object",
            "properties": {
                "create_time": {
                    "type": "integer",
                    "default": 1550153498
                },
                "hash": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "login": {
                    "type": "string",
                    "default": "<EMAIL>"
                },
                "type": {
                    "type": "integer",
                    "default": 1
                },
                "type_name": {
                    "type": "string"
                }
            }
        },
        "user.RspSSO": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspLogin"
                    }
                },
                "nick": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                },
                "options": {
                    "type": "object",
                    "properties": {
                        "disable_workspace_creation": {
                            "type": "boolean"
                        }
                    }
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.PermName"
                    }
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.ShortRowPermItem"
                    }
                },
                "photo": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspWorkspaceRole"
                    }
                },
                "sso": {},
                "tokens": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.TokenRsp"
                    }
                },
                "workspaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspWorkspace"
                    }
                }
            }
        },
        "user.RspShort": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspLogin"
                    }
                },
                "nick": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                },
                "photo": {
                    "type": "string",
                    "default": "https://content.com/Z6UvD0D6Ug2RER137JiW"
                }
            }
        },
        "user.RspWorkspace": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "disable_invites": {
                    "type": "boolean"
                },
                "groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspWorkspaceGroup"
                    }
                },
                "id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.PermName"
                    }
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.ShortRowPermItem"
                    }
                },
                "photo": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspWorkspaceRole"
                    }
                },
                "sim_url": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "default": "active"
                },
                "user_status": {
                    "type": "string",
                    "default": "active"
                }
            }
        },
        "user.RspWorkspaceGroup": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "is_member": {
                    "type": "boolean",
                    "default": true
                },
                "is_owner": {
                    "type": "boolean",
                    "default": true
                },
                "name": {
                    "type": "string",
                    "default": "My group"
                }
            }
        },
        "user.RspWorkspaceItem": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "default": "2022-10-10 09-09-09"
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "photo": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "default": "active"
                }
            }
        },
        "user.RspWorkspaceRole": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My roel"
                }
            }
        },
        "user.SSOReq": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "user.ShortRow": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "logins": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RspLogin"
                    }
                },
                "nick": {
                    "type": "string"
                },
                "perms": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.ShortRowPermItem"
                    }
                },
                "photo": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/user.RoleRow"
                    }
                },
                "status": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "user.ShortRowPermItem": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "type": {
                    "type": "string",
                    "default": "control"
                }
            }
        },
        "user.TokenRsp": {
            "type": "object",
            "properties": {
                "type": {
                    "type": "string",
                    "default": "keycloak"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "user.UpdateReq": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "name": {
                    "type": "string"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                }
            }
        },
        "user.UpdateStatusReq": {
            "type": "object",
            "properties": {
                "status": {
                    "description": "User status, available values: active/blocked",
                    "type": "string",
                    "default": "blocked"
                }
            }
        },
        "user.UpdateUser": {
            "type": "object",
            "properties": {
                "active": {
                    "description": "Active is the marker to add or remove from the list",
                    "type": "boolean"
                },
                "role_id": {
                    "type": "integer",
                    "default": 1
                },
                "user_id": {
                    "type": "integer",
                    "default": 1
                }
            }
        },
        "user.VerifyRsp": {
            "type": "object",
            "properties": {
                "verified": {
                    "type": "boolean"
                }
            }
        },
        "workspace.AISetting": {
            "type": "object",
            "properties": {
                "api_key": {
                    "type": "string"
                },
                "provider": {
                    "type": "string"
                }
            }
        },
        "workspace.CreateReq": {
            "type": "object",
            "properties": {
                "apis": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.CreateReqAPI"
                    }
                },
                "attributes": {
                    "type": "object"
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "skip_on_exist": {
                    "type": "boolean"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.CreateReqUsers"
                    }
                }
            }
        },
        "workspace.CreateReqAPI": {
            "type": "object",
            "properties": {
                "owner_id": {
                    "type": "integer",
                    "default": 45317
                },
                "url": {
                    "type": "string",
                    "default": "http://localhost:8080"
                },
                "user_id": {
                    "type": "integer",
                    "default": 45317
                }
            }
        },
        "workspace.CreateReqUsers": {
            "type": "object",
            "properties": {
                "role_id": {
                    "type": "integer",
                    "default": 1
                },
                "user_id": {
                    "type": "integer",
                    "default": 45317
                }
            }
        },
        "workspace.CreateRsp": {
            "type": "object",
            "properties": {
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                }
            }
        },
        "workspace.OwnerRow": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string",
                    "default": "#000000"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Ivan Ivanov"
                },
                "photo": {
                    "type": "string",
                    "default": "https://account.corezoid.com/avatars/0.jpg"
                }
            }
        },
        "workspace.RoleRow": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "Member"
                }
            }
        },
        "workspace.Row": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean",
                    "default": true
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                }
            }
        },
        "workspace.RspItem": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "default": "2022-10-10 09-09-09"
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "owners": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.OwnerRow"
                    }
                },
                "photo": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.RoleRow"
                    }
                },
                "status": {
                    "type": "string",
                    "default": "active"
                },
                "user_status": {
                    "type": "string",
                    "default": "active"
                }
            }
        },
        "workspace.RspMeta": {
            "type": "object",
            "properties": {
                "total": {
                    "type": "integer"
                }
            }
        },
        "workspace.Settings": {
            "type": "object",
            "properties": {
                "ai_settings": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.AISetting"
                    }
                },
                "allowed_domains": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "auto_recording": {
                    "type": "boolean"
                },
                "disable_invites": {
                    "type": "boolean"
                },
                "file_ttl": {
                    "type": "integer"
                },
                "forbidden_domains": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "sim_client": {
                    "type": "string"
                },
                "transcription_lang": {
                    "type": "string"
                }
            }
        },
        "workspace.ShortRsp": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.RspItem"
                    }
                },
                "meta": {
                    "$ref": "#/definitions/workspace.RspMeta"
                }
            }
        },
        "workspace.Workspace": {
            "type": "object",
            "properties": {
                "color": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "default": "2022-10-10 09-09-09"
                },
                "ext_id": {
                    "type": "string",
                    "default": "ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"
                },
                "id": {
                    "type": "integer",
                    "default": 1
                },
                "name": {
                    "type": "string",
                    "default": "My workspace"
                },
                "owners": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/workspace.OwnerRow"
                    }
                },
                "photo": {
                    "type": "string"
                },
                "settings": {
                    "$ref": "#/definitions/workspace.Settings"
                },
                "status": {
                    "type": "string",
                    "default": "active"
                }
            }
        }
    },
    "securityDefinitions": {
        "BasicAuth": {
            "type": "basic"
        }
    }
}`

// SwaggerInfoclient holds exported Swagger Info so clients can modify it
var SwaggerInfoclient = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "client",
	SwaggerTemplate:  docTemplateclient,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfoclient.InstanceName(), SwaggerInfoclient)
}
