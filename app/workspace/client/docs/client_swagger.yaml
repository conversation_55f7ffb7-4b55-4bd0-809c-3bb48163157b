definitions:
  api.CreateReq:
    properties:
      color:
        type: string
      meta:
        additionalProperties: {}
        type: object
      name:
        type: string
      photo:
        type: string
      scopes:
        items:
          $ref: '#/definitions/api.Scope'
        type: array
      secret:
        type: string
      url:
        type: string
    type: object
  api.CreateReqNoRightsCheking:
    properties:
      color:
        type: string
      name:
        type: string
      photo:
        type: string
      scopes:
        items:
          $ref: '#/definitions/api.Scope'
        type: array
      url:
        type: string
      workspace_id:
        default: afdasfsad
        type: string
    type: object
  api.PatchReq:
    properties:
      meta:
        additionalProperties: {}
        type: object
    type: object
  api.Row:
    properties:
      access_token:
        type: string
      color:
        type: string
      created_at:
        type: string
      id:
        type: integer
      meta:
        additionalProperties: {}
        type: object
      name:
        type: string
      owner_id:
        type: integer
      owner_name:
        type: string
      photo:
        type: string
      scopes:
        items:
          $ref: '#/definitions/api.Scope'
        type: array
      status:
        type: string
      url:
        type: string
    type: object
  api.Rsp:
    properties:
      access_token:
        type: string
      color:
        default: '#000000'
        type: string
      groups:
        items:
          $ref: '#/definitions/api.RspGroup'
        type: array
      id:
        default: 1
        type: integer
      nick:
        default: Process 123
        type: string
      owner_id:
        default: 1
        type: integer
      photo:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
      status:
        default: active
        description: 'API User status, values: active/blocked'
        type: string
      url:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
      workspace_id:
        default: "1"
        type: string
      workspace_name:
        type: string
    type: object
  api.RspGroup:
    properties:
      id:
        default: 1
        type: integer
      is_member:
        default: true
        type: boolean
      is_owner:
        default: true
        type: boolean
      name:
        default: My group
        type: string
    type: object
  api.Scope:
    properties:
      id:
        default: actors:read_only
        type: string
      type:
        default: scopes.control
        type: string
    type: object
  client.CredentialsRsp:
    properties:
      algorithm:
        type: string
      private_key:
        type: string
      public_key:
        type: string
    type: object
  client.UpdateReq:
    properties:
      notify_url:
        type: string
      subscribe_to:
        example:
        - all
        items:
          type: string
        type: array
    type: object
  desktop.SyncReq:
    properties:
      access_token:
        type: string
    required:
    - access_token
    type: object
  group.CreateReq:
    properties:
      meta:
        additionalProperties: {}
        type: object
      name:
        type: string
      users:
        description: list of users and api users
        items:
          type: integer
        type: array
    type: object
  group.OwnerRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: Ivan Ivanov
        type: string
    type: object
  group.RowAPIUser:
    properties:
      color:
        default: '#000000'
        type: string
      id:
        default: 1
        type: integer
      name:
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
    type: object
  group.RowUser:
    properties:
      color:
        type: string
      id:
        default: 1
        type: integer
      logins:
        items:
          $ref: '#/definitions/group.RspLogin'
        type: array
      name:
        type: string
      photo:
        type: string
    type: object
  group.Rsp:
    properties:
      api_count:
        default: 1
        type: integer
      created_at:
        type: string
      id:
        default: 1
        type: integer
      meta:
        additionalProperties: {}
        type: object
      name:
        default: Default
        type: string
      owners:
        items:
          $ref: '#/definitions/group.OwnerRow'
        type: array
      user_count:
        default: 1
        type: integer
    type: object
  group.RspLogin:
    properties:
      login:
        default: <EMAIL>
        type: string
      type:
        default: 1
        type: integer
    type: object
  group.UpdateReq:
    properties:
      meta:
        additionalProperties: {}
        type: object
      name:
        type: string
      owner_id:
        type: integer
    type: object
  group.UpdateUser:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      id:
        default: 1
        description: ID is user_id or api user_id
        type: integer
    type: object
  invite.CreateReq:
    properties:
      login:
        default: <EMAIL>
        type: string
      redirect_uri:
        default: https://admin.corezoid.com
        type: string
      role_id:
        default: 1
        type: integer
      type:
        default: email
        description: 'available types: api, google, facebook, ldap, oauth_pb, phone,
          corezoid, github, apple, keycloak'
        type: string
    type: object
  invite.Row:
    properties:
      created_at:
        type: string
      id:
        type: integer
      redirect_uri:
        type: string
      role_id:
        type: integer
      type:
        type: integer
      user_id:
        type: integer
    type: object
  license.AttributeData:
    properties:
      value:
        type: integer
    type: object
  license.Attributes:
    properties:
      rps:
        $ref: '#/definitions/license.AttributeData'
      state_changes:
        $ref: '#/definitions/license.AttributeData'
      storage:
        $ref: '#/definitions/license.AttributeData'
      support:
        $ref: '#/definitions/license.AttributeData'
      users:
        $ref: '#/definitions/license.AttributeData'
      white_label:
        $ref: '#/definitions/license.AttributeData'
    type: object
  license.InvoiceInfo:
    properties:
      file_url:
        type: string
    type: object
  license.PaymentInfo:
    properties:
      invoice:
        $ref: '#/definitions/license.InvoiceInfo'
    type: object
  license.RootRsp:
    properties:
      account_license_module:
        type: boolean
      cluster_id:
        type: string
      company_id:
        type: string
      created_time:
        type: string
      is_allow:
        type: boolean
      issuer:
        type: string
      max_active_procs:
        type: integer
      max_rpm:
        type: integer
      max_rps:
        type: integer
      max_scpm:
        type: integer
      max_storage_size:
        type: integer
      max_task_size_process:
        type: integer
      max_task_size_state_diagram:
        type: integer
      max_users:
        type: integer
      min_timer:
        type: integer
      multi_tenancy:
        type: boolean
      pub_key:
        type: string
      time_to_expire:
        type: integer
      time_to_start:
        type: integer
    type: object
  license.RspDetailed:
    properties:
      attributes:
        $ref: '#/definitions/license.Attributes'
      comment:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: integer
      multitenant:
        type: boolean
      next_payment_at:
        type: string
      payment:
        $ref: '#/definitions/license.PaymentInfo'
      payment_type:
        description: 'available values: invoice, stripe'
        enum:
        - invoice
        - stripe
        type: string
      period:
        description: 'available values: 1, 12'
        type: integer
      price:
        type: number
      reason:
        type: string
      status:
        description: 'available values: active, pending, expired, canceled'
        enum:
        - active
        - pending
        - ' expired'
        - canceled
        type: string
      type:
        description: 'available values: cloud, in_house, desktop'
        enum:
        - cloud
        - in_house
        - desktop
        type: string
      workspaces:
        items:
          $ref: '#/definitions/license.WorkspaceInfo'
        type: array
    type: object
  license.RspItem:
    properties:
      created_at:
        type: string
      expires_at:
        type: string
      id:
        type: integer
      invoice:
        $ref: '#/definitions/license.InvoiceInfo'
      next_payment_at:
        type: string
      payment_type:
        enum:
        - invoice
        - stripe
        type: string
      period:
        type: integer
      price:
        type: number
      reason:
        type: string
      status:
        description: 'available values: active, pending, expired, canceled'
        enum:
        - active
        - pending
        - ' expired'
        - canceled
        type: string
      type:
        description: 'available values: cloud, in_house, desktop'
        enum:
        - cloud
        - in_house
        - desktop
        type: string
    type: object
  license.SetStateChangesReq:
    properties:
      user_id:
        type: integer
      value:
        type: integer
      workspace_id:
        type: string
    type: object
  license.UpdateEnvReq:
    properties:
      env:
        type: string
      pub_key:
        type: string
    type: object
  license.UsersWithNoStateChanges:
    properties:
      list:
        items:
          type: integer
        type: array
    type: object
  license.WorkspaceInfo:
    properties:
      id:
        type: string
    type: object
  perm.CreateReq:
    properties:
      active_in_roles:
        items:
          type: integer
        type: array
      ext_id:
        default: actors
        type: string
      name:
        default: actors
        type: string
    type: object
  perm.Rsp:
    properties:
      active_in_roles:
        items:
          type: integer
        type: array
      ext_id:
        default: actors
        type: string
      id:
        default: 1
        type: integer
      name:
        default: actors
        type: string
    type: object
  role.AvailableRsp:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: Default
        type: string
    type: object
  scope.CreateReq:
    properties:
      ext_id:
        default: users.readonly
        type: string
      name:
        default: Read only account users information
        type: string
    type: object
  scope.Rsp:
    properties:
      ext_id:
        default: actors
        type: string
      id:
        default: 1
        type: integer
      name:
        default: actors
        type: string
    type: object
  swaggwebhook.AISetting:
    properties:
      api_key:
        type: string
      provider:
        type: string
    type: object
  swaggwebhook.ExampleInvite:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.Invite'
      id:
        default: 1
        type: integer
      name:
        default: invite/del
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleLicense:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.License'
      id:
        default: 1
        type: integer
      name:
        default: license/attrs/update
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleUserLogin:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.UserLogin'
      id:
        default: 1
        type: integer
      name:
        default: user/login
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleUserLogout:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.UserLogout'
      id:
        default: 1
        type: integer
      name:
        default: user/logout
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleUserSet:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.UserSet'
      id:
        default: 1
        type: integer
      name:
        default: user/set
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSAPIUserSet:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSAPIUserSet'
      id:
        default: 1
        type: integer
      name:
        default: workspace/api_user/set
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSAdd:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSAdd'
      id:
        default: 1
        type: integer
      name:
        default: workspace/add
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSDel:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSDel'
      id:
        default: 1
        type: integer
      name:
        default: workspace/del
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSGroupAdd:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSGroupAdd'
      id:
        default: 1
        type: integer
      name:
        default: workspace/group/add
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSGroupDel:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSGroupDel'
      id:
        default: 1
        type: integer
      name:
        default: workspace/group/del
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSGroupSet:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSGroupSet'
      id:
        default: 1
        type: integer
      name:
        default: workspace/group/set
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSGroupUserSet:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSGroupUserSet'
      id:
        default: 1
        type: integer
      name:
        default: workspace/group/user/set
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSSet:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSSet'
      id:
        default: 1
        type: integer
      name:
        default: workspace/set
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSSync:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSAdd'
      id:
        default: 1
        type: integer
      name:
        default: workspace/sync
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSUserPerm:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSUserPerm'
      id:
        default: 1
        type: integer
      name:
        default: workspace/user/perm/set
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.ExampleWSUserSet:
    properties:
      created_at:
        default: 2023-01-28T21:19:42
        type: string
      data:
        $ref: '#/definitions/swaggwebhook.WSUserSet'
      id:
        default: 1
        type: integer
      name:
        default: workspace/user/set
        type: string
      workspace_id:
        default: beb78608-4659-4529-bfa6-905975563f21
        type: string
    type: object
  swaggwebhook.Invite:
    properties:
      client_id:
        type: string
      client_name:
        type: string
      id:
        type: integer
      login:
        type: string
      user_id:
        type: integer
    type: object
  swaggwebhook.License:
    properties:
      attributes:
        additionalProperties:
          type: integer
        type: object
      id:
        type: integer
      user_id:
        type: integer
      workspaces:
        items:
          type: string
        type: array
    type: object
  swaggwebhook.Settings:
    properties:
      ai_settings:
        items:
          $ref: '#/definitions/swaggwebhook.AISetting'
        type: array
      allowed_domains:
        items:
          type: string
        type: array
      auto_recording:
        type: boolean
      disable_invites:
        type: boolean
      file_ttl:
        type: integer
      forbidden_domains:
        items:
          type: string
        type: array
      sim_client:
        type: string
      transcription_lang:
        type: string
    type: object
  swaggwebhook.UserLogin:
    properties:
      user_id:
        type: integer
    type: object
  swaggwebhook.UserLogout:
    properties:
      user_id:
        type: integer
    type: object
  swaggwebhook.UserSet:
    properties:
      color:
        default: https://color.com
        type: string
      id:
        type: integer
      lang:
        type: string
      name:
        type: string
      photo:
        default: https://photo.com
        type: string
      status:
        default: active
        enum:
        - active
        - blocked_by_license
        type: string
    type: object
  swaggwebhook.WSAPIUserSet:
    properties:
      color:
        default: '#000000'
        type: string
      id:
        default: 1
        type: integer
      name:
        default: Ivan
        type: string
      photo:
        default: https://google.com
        type: string
      scopes:
        items:
          $ref: '#/definitions/swaggwebhook.WSAPIUserSetItem'
        type: array
      url:
        default: https://google.com
        type: string
    type: object
  swaggwebhook.WSAPIUserSetItem:
    properties:
      ids:
        items:
          type: string
        type: array
      type:
        type: string
    type: object
  swaggwebhook.WSAdd:
    properties:
      color:
        default: https://color.com
        type: string
      name:
        default: My group
        type: string
      owner_ids:
        items:
          type: integer
        type: array
      photo:
        default: https://photo.com
        type: string
    type: object
  swaggwebhook.WSDel:
    properties:
      author_id:
        type: integer
    type: object
  swaggwebhook.WSGroupAdd:
    properties:
      id:
        default: 1
        type: integer
      meta:
        additionalProperties: {}
        type: object
      name:
        default: My group name
        type: string
      owner_ids:
        items:
          type: integer
        type: array
      users:
        items:
          $ref: '#/definitions/swaggwebhook.WSGroupAddItem'
        type: array
    type: object
  swaggwebhook.WSGroupAddItem:
    properties:
      id:
        type: integer
      type:
        type: string
    type: object
  swaggwebhook.WSGroupDel:
    properties:
      id:
        default: 1
        type: integer
      meta:
        additionalProperties: {}
        type: object
      name:
        default: My group
        type: string
      owner_ids:
        items:
          type: integer
        type: array
    type: object
  swaggwebhook.WSGroupSet:
    properties:
      id:
        default: 1
        type: integer
      meta:
        additionalProperties: {}
        type: object
      name:
        default: My group
        type: string
      owner_ids:
        items:
          type: integer
        type: array
    type: object
  swaggwebhook.WSGroupUserSet:
    properties:
      id:
        default: 1
        type: integer
      users:
        items:
          $ref: '#/definitions/swaggwebhook.WSGroupUserSetItem'
        type: array
    type: object
  swaggwebhook.WSGroupUserSetItem:
    properties:
      active:
        type: boolean
      id:
        type: integer
      type:
        type: string
    type: object
  swaggwebhook.WSSet:
    properties:
      color:
        default: https://color.com
        type: string
      name:
        default: My group
        type: string
      owner_ids:
        items:
          type: integer
        type: array
      photo:
        default: https://photo.com
        type: string
      settings:
        $ref: '#/definitions/swaggwebhook.Settings'
    type: object
  swaggwebhook.WSUserPerm:
    properties:
      id:
        type: integer
      perms:
        items:
          $ref: '#/definitions/swaggwebhook.WSUserPermItem'
        type: array
    type: object
  swaggwebhook.WSUserPermItem:
    properties:
      ids:
        items:
          type: string
        type: array
      type:
        default: control
        type: string
    type: object
  swaggwebhook.WSUserSet:
    properties:
      users:
        items:
          $ref: '#/definitions/swaggwebhook.WSUserSetItem'
        type: array
    type: object
  swaggwebhook.WSUserSetItem:
    properties:
      active:
        description: deprecated
        type: boolean
      author_id:
        description: id of user who changed status
        type: integer
      color:
        default: https://color.com
        type: string
      id:
        type: integer
      name:
        type: string
      obj_owner_ids:
        items:
          $ref: '#/definitions/swaggwebhook.WSUserSetItemObjOwnerID'
        type: array
      perms:
        items:
          $ref: '#/definitions/swaggwebhook.WSUserPermItem'
        type: array
      photo:
        default: https://photo.com
        type: string
      status:
        description: 'status: added, blocked, active,deleted'
        type: string
    type: object
  swaggwebhook.WSUserSetItemObjOwnerID:
    properties:
      id:
        type: integer
    type: object
  user.AddReq:
    properties:
      role_id:
        description: 'example: 1'
        type: integer
      user_id:
        description: 'example: 1'
        type: integer
    type: object
  user.CreateArrayReq:
    properties:
      items:
        items:
          properties:
            hash:
              default: "123"
              type: string
            login:
              default: <EMAIL>
              type: string
            login_type:
              default: google
              description: 'available: api, google, facebook, ldap, oauth, phone,
                corezoid, github, apple, keycloak, sso, microsoft'
              type: string
            nick:
              default: Ivan Ivanov
              type: string
          type: object
        type: array
    type: object
  user.CreateReq:
    properties:
      login:
        default: <EMAIL>
        type: string
      login_type:
        default: google
        description: 'available: google, facebook, ldap, oauth_pb, phone, github,
          apple, microsoft'
        type: string
      nick:
        default: Ivan Ivanov
        type: string
      skip_on_exist:
        type: boolean
    type: object
  user.CreateUserRsp:
    properties:
      id:
        type: integer
      nick:
        type: string
    type: object
  user.OnlineReq:
    properties:
      users:
        description: Users is the list of user ids (max size = 1000)
        items:
          type: integer
        type: array
    type: object
  user.PermName:
    properties:
      id:
        default: actorsBag
        type: string
      type:
        default: perms.control
        type: string
    type: object
  user.RoleRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: my role
        type: string
    type: object
  user.Rsp:
    properties:
      color:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
      id:
        default: 1
        type: integer
      logins:
        items:
          $ref: '#/definitions/user.RspLogin'
        type: array
      nick:
        default: Ivan Ivanov
        type: string
      options:
        properties:
          disable_workspace_creation:
            type: boolean
        type: object
      permissions:
        items:
          $ref: '#/definitions/user.PermName'
        type: array
      perms:
        items:
          $ref: '#/definitions/user.ShortRowPermItem'
        type: array
      photo:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
      roles:
        items:
          $ref: '#/definitions/user.RspWorkspaceRole'
        type: array
      tokens:
        items:
          $ref: '#/definitions/user.TokenRsp'
        type: array
      workspaces:
        items:
          $ref: '#/definitions/user.RspWorkspace'
        type: array
    type: object
  user.RspLogin:
    properties:
      create_time:
        default: 1550153498
        type: integer
      hash:
        type: string
      id:
        default: 1
        type: integer
      login:
        default: <EMAIL>
        type: string
      type:
        default: 1
        type: integer
      type_name:
        type: string
    type: object
  user.RspSSO:
    properties:
      color:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
      id:
        default: 1
        type: integer
      logins:
        items:
          $ref: '#/definitions/user.RspLogin'
        type: array
      nick:
        default: Ivan Ivanov
        type: string
      options:
        properties:
          disable_workspace_creation:
            type: boolean
        type: object
      permissions:
        items:
          $ref: '#/definitions/user.PermName'
        type: array
      perms:
        items:
          $ref: '#/definitions/user.ShortRowPermItem'
        type: array
      photo:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
      roles:
        items:
          $ref: '#/definitions/user.RspWorkspaceRole'
        type: array
      sso: {}
      tokens:
        items:
          $ref: '#/definitions/user.TokenRsp'
        type: array
      workspaces:
        items:
          $ref: '#/definitions/user.RspWorkspace'
        type: array
    type: object
  user.RspShort:
    properties:
      color:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
      id:
        default: 1
        type: integer
      logins:
        items:
          $ref: '#/definitions/user.RspLogin'
        type: array
      nick:
        default: Ivan Ivanov
        type: string
      photo:
        default: https://content.com/Z6UvD0D6Ug2RER137JiW
        type: string
    type: object
  user.RspWorkspace:
    properties:
      color:
        type: string
      disable_invites:
        type: boolean
      groups:
        items:
          $ref: '#/definitions/user.RspWorkspaceGroup'
        type: array
      id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      name:
        default: My workspace
        type: string
      permissions:
        items:
          $ref: '#/definitions/user.PermName'
        type: array
      perms:
        items:
          $ref: '#/definitions/user.ShortRowPermItem'
        type: array
      photo:
        type: string
      roles:
        items:
          $ref: '#/definitions/user.RspWorkspaceRole'
        type: array
      sim_url:
        type: string
      status:
        default: active
        type: string
      user_status:
        default: active
        type: string
    type: object
  user.RspWorkspaceGroup:
    properties:
      id:
        default: 1
        type: integer
      is_member:
        default: true
        type: boolean
      is_owner:
        default: true
        type: boolean
      name:
        default: My group
        type: string
    type: object
  user.RspWorkspaceItem:
    properties:
      color:
        type: string
      created_at:
        default: 2022-10-10 09-09-09
        type: string
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      id:
        default: 1
        type: integer
      name:
        default: My workspace
        type: string
      photo:
        type: string
      status:
        default: active
        type: string
    type: object
  user.RspWorkspaceRole:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: My roel
        type: string
    type: object
  user.SSOReq:
    properties:
      token:
        type: string
    type: object
  user.ShortRow:
    properties:
      color:
        type: string
      id:
        type: integer
      logins:
        items:
          $ref: '#/definitions/user.RspLogin'
        type: array
      nick:
        type: string
      perms:
        items:
          $ref: '#/definitions/user.ShortRowPermItem'
        type: array
      photo:
        type: string
      roles:
        items:
          $ref: '#/definitions/user.RoleRow'
        type: array
      status:
        type: string
      url:
        type: string
    type: object
  user.ShortRowPermItem:
    properties:
      ids:
        items:
          type: string
        type: array
      type:
        default: control
        type: string
    type: object
  user.TokenRsp:
    properties:
      type:
        default: keycloak
        type: string
      value:
        type: string
    type: object
  user.UpdateReq:
    properties:
      color:
        default: '#000000'
        type: string
      name:
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
    type: object
  user.UpdateStatusReq:
    properties:
      status:
        default: blocked
        description: 'User status, available values: active/blocked'
        type: string
    type: object
  user.UpdateUser:
    properties:
      active:
        description: Active is the marker to add or remove from the list
        type: boolean
      role_id:
        default: 1
        type: integer
      user_id:
        default: 1
        type: integer
    type: object
  user.VerifyRsp:
    properties:
      verified:
        type: boolean
    type: object
  workspace.AISetting:
    properties:
      api_key:
        type: string
      provider:
        type: string
    type: object
  workspace.CreateReq:
    properties:
      apis:
        items:
          $ref: '#/definitions/workspace.CreateReqAPI'
        type: array
      attributes:
        type: object
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      name:
        default: My workspace
        type: string
      skip_on_exist:
        type: boolean
      users:
        items:
          $ref: '#/definitions/workspace.CreateReqUsers'
        type: array
    type: object
  workspace.CreateReqAPI:
    properties:
      owner_id:
        default: 45317
        type: integer
      url:
        default: http://localhost:8080
        type: string
      user_id:
        default: 45317
        type: integer
    type: object
  workspace.CreateReqUsers:
    properties:
      role_id:
        default: 1
        type: integer
      user_id:
        default: 45317
        type: integer
    type: object
  workspace.CreateRsp:
    properties:
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
    type: object
  workspace.OwnerRow:
    properties:
      color:
        default: '#000000'
        type: string
      id:
        default: 1
        type: integer
      name:
        default: Ivan Ivanov
        type: string
      photo:
        default: https://account.corezoid.com/avatars/0.jpg
        type: string
    type: object
  workspace.RoleRow:
    properties:
      id:
        default: 1
        type: integer
      name:
        default: Member
        type: string
    type: object
  workspace.Row:
    properties:
      active:
        default: true
        type: boolean
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      name:
        default: My workspace
        type: string
    type: object
  workspace.RspItem:
    properties:
      color:
        type: string
      created_at:
        default: 2022-10-10 09-09-09
        type: string
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      id:
        default: 1
        type: integer
      name:
        default: My workspace
        type: string
      owners:
        items:
          $ref: '#/definitions/workspace.OwnerRow'
        type: array
      photo:
        type: string
      roles:
        items:
          $ref: '#/definitions/workspace.RoleRow'
        type: array
      status:
        default: active
        type: string
      user_status:
        default: active
        type: string
    type: object
  workspace.RspMeta:
    properties:
      total:
        type: integer
    type: object
  workspace.Settings:
    properties:
      ai_settings:
        items:
          $ref: '#/definitions/workspace.AISetting'
        type: array
      allowed_domains:
        items:
          type: string
        type: array
      auto_recording:
        type: boolean
      disable_invites:
        type: boolean
      file_ttl:
        type: integer
      forbidden_domains:
        items:
          type: string
        type: array
      sim_client:
        type: string
      transcription_lang:
        type: string
    type: object
  workspace.ShortRsp:
    properties:
      data:
        items:
          $ref: '#/definitions/workspace.RspItem'
        type: array
      meta:
        $ref: '#/definitions/workspace.RspMeta'
    type: object
  workspace.Workspace:
    properties:
      color:
        type: string
      created_at:
        default: 2022-10-10 09-09-09
        type: string
      ext_id:
        default: ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda
        type: string
      id:
        default: 1
        type: integer
      name:
        default: My workspace
        type: string
      owners:
        items:
          $ref: '#/definitions/workspace.OwnerRow'
        type: array
      photo:
        type: string
      settings:
        $ref: '#/definitions/workspace.Settings'
      status:
        default: active
        type: string
    type: object
info:
  contact: {}
paths:
  /apis:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: CreateReqNoRightsCheking
        required: true
        schema:
          $ref: '#/definitions/api.CreateReqNoRightsCheking'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/api.Rsp'
      security:
      - BasicAuth: []
      summary: Create api user
      tags:
      - API Users
  /apis/me:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Rsp'
      security:
      - BasicAuth: []
      summary: Get api details
      tags:
      - API Users
  /apis/scopes/{type}/{scopes}/verify:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA api token
        in: header
        name: sa-api-token
        required: true
        type: string
      - description: scope type (control/corezoid)
        in: path
        name: type
        required: true
        type: string
      - description: scopes (comma separated)
        in: path
        name: scopes
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Rsp'
      security:
      - BasicAuth: []
      summary: Verify scopes
      tags:
      - API Users
  /clients/credentials:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/client.CredentialsRsp'
      security:
      - BasicAuth: []
      summary: Get environment credentials(public_key and private_key)
      tags:
      - Client
  /clients/subscribe:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: UpdateReq
        required: true
        schema:
          $ref: '#/definitions/client.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update client info
      tags:
      - Client
  /desktops/{uuid}/sync:
    post:
      consumes:
      - application/json
      parameters:
      - description: desktop uuid
        in: path
        name: uuid
        required: true
        type: integer
      - description: flag to get license file
        in: query
        name: license
        type: boolean
      - description: body
        in: body
        name: SyncReq
        required: true
        schema:
          $ref: '#/definitions/desktop.SyncReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Sync desktop state
      tags:
      - Client
  /invite/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleInvite'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleInvite'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Add invite
      tags:
      - Webhook
  /invite/confirm:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleInvite'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleInvite'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Confirm invite
      tags:
      - Webhook
  /invite/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleInvite'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleInvite'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete invite
      tags:
      - Webhook
  /license/attrs/update:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleLicense'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleLicense'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: License attr update
      tags:
      - Webhook
  /licenses:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      - collectionFormat: csv
        description: filter by statuses
        in: query
        items:
          type: string
        name: statuses
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/license.RspItem'
            type: array
      security:
      - BasicAuth: []
      summary: List all license
      tags:
      - License
  /licenses/pub_keys/env:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: UpdateEnvReq
        required: true
        schema:
          $ref: '#/definitions/license.UpdateEnvReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Link the license with the env_id via pub_key
      tags:
      - License
  /licenses/root:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RootRsp'
      security:
      - BasicAuth: []
      summary: Return root license info
      tags:
      - License
  /licenses/state_changes/used:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]SetStateChangesReq'
        required: true
        schema:
          items:
            $ref: '#/definitions/license.SetStateChangesReq'
          type: array
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Set used state changes
      tags:
      - License management
  /licenses/users/{user_id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RspDetailed'
      security:
      - BasicAuth: []
      summary: Get license by user
      tags:
      - License
  /licenses/users/with_no_state_changes:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.UsersWithNoStateChanges'
      security:
      - BasicAuth: []
      summary: Get users with no state changes
      tags:
      - License
  /licenses/workspaces/{workspace_id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/license.RspDetailed'
      security:
      - BasicAuth: []
      summary: Get license by workspace
      tags:
      - License
  /perms/{type}:
    get:
      consumes:
      - application/json
      parameters:
      - description: perm type
        in: path
        name: type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/perm.Rsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all perms by type
      tags:
      - Perms
    post:
      consumes:
      - application/json
      parameters:
      - description: perm type (control/corezoid)
        in: path
        name: type
        required: true
        type: string
      - description: body
        in: body
        name: '[]CreateReq'
        required: true
        schema:
          items:
            $ref: '#/definitions/perm.CreateReq'
          type: array
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Create perms and assign to roles
      tags:
      - Perms
  /perms/{type}/{ext_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: perm type
        in: path
        name: type
        required: true
        type: string
      - description: perm ext_id
        in: path
        name: ext_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete perm
      tags:
      - Perms
  /scopes/{type}:
    get:
      consumes:
      - application/json
      parameters:
      - description: scope type (control/corezoid)
        in: path
        name: type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/scope.Rsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all scopes by type
      tags:
      - Scopes
    post:
      consumes:
      - application/json
      parameters:
      - description: scope type (control/corezoid)
        in: path
        name: type
        required: true
        type: string
      - description: body
        in: body
        name: '[]CreateReq'
        required: true
        schema:
          items:
            $ref: '#/definitions/scope.CreateReq'
          type: array
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Create scope and assign to roles
      tags:
      - Scopes
  /scopes/{type}/{ext_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: scope type (control/corezoid)
        in: path
        name: type
        required: true
        type: string
      - description: scope ext_id
        in: path
        name: ext_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete scope
      tags:
      - Scopes
  /user/login:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleUserLogin'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleUserLogin'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: User login
      tags:
      - Webhook
  /user/logout:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleUserLogout'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleUserLogout'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: User logout
      tags:
      - Webhook
  /user/set:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleUserSet'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleUserSet'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user
      tags:
      - Webhook
  /users:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/user.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/user.CreateUserRsp'
      security:
      - BasicAuth: []
      summary: Create user
      tags:
      - Users
    put:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        required: true
        type: string
      - description: body
        in: body
        name: UpdateReq
        required: true
        schema:
          $ref: '#/definitions/user.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user info
      tags:
      - Users
  /users/{user_id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/user.UpdateStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user status
      tags:
      - Users
  /users/array:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: CreateArrayReq
        required: true
        schema:
          $ref: '#/definitions/user.CreateArrayReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            items:
              $ref: '#/definitions/user.CreateUserRsp'
            type: array
      security:
      - BasicAuth: []
      summary: Create users
      tags:
      - Users
  /users/me:
    get:
      consumes:
      - application/json
      parameters:
      - description: user login
        in: header
        name: sa-login
        type: string
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.Rsp'
      security:
      - BasicAuth: []
      summary: Get user details
      tags:
      - Users
  /users/me/short:
    get:
      consumes:
      - application/json
      parameters:
      - description: user login
        in: header
        name: sa-login
        type: string
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.RspShort'
      security:
      - BasicAuth: []
      summary: Get user details
      tags:
      - Users
  /users/me/workspaces:
    get:
      consumes:
      - application/json
      parameters:
      - description: user login
        in: header
        name: sa-login
        type: string
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/user.RspWorkspaceItem'
            type: array
      security:
      - BasicAuth: []
      summary: Get user workspaces where he is the owner
      tags:
      - Users
  /users/online:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: OnlineReq
        required: true
        schema:
          $ref: '#/definitions/user.OnlineReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Set the status of user sessions to "online"
      tags:
      - Users
  /users/sso:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: SSOReq
        required: true
        schema:
          $ref: '#/definitions/user.SSOReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.RspSSO'
      security:
      - BasicAuth: []
      summary: Authorize user via SSO (and create if needed)
      tags:
      - Users
  /workspace/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSAdd'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSAdd'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Create workspace
      tags:
      - Webhook
  /workspace/api_user/set:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSAPIUserSet'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSAPIUserSet'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update api user info
      tags:
      - Webhook
  /workspace/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSDel'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSDel'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete workspace
      tags:
      - Webhook
  /workspace/group/add:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSGroupAdd'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSGroupAdd'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Create new group
      tags:
      - Webhook
  /workspace/group/del:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSGroupDel'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSGroupDel'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete group
      tags:
      - Webhook
  /workspace/group/set:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSGroupSet'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSGroupSet'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Change group info
      tags:
      - Webhook
  /workspace/group/user/set:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSGroupUserSet'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSGroupUserSet'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Change group member list
      tags:
      - Webhook
  /workspace/set:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSSet'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSSet'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update workspace
      tags:
      - Webhook
  /workspace/sync:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSSync'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSSync'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Sync workspace
      tags:
      - Webhook
  /workspace/user/perm/set:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSUserPerm'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSUserPerm'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Change user permission list
      tags:
      - Webhook
  /workspace/user/set:
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: '[]ExampleWSUserSet'
        required: true
        schema:
          items:
            $ref: '#/definitions/swaggwebhook.ExampleWSUserSet'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Change member list in the workspace
      tags:
      - Webhook
  /workspaces:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/workspace.ShortRsp'
      security:
      - BasicAuth: []
      summary: List all workspaces
      tags:
      - Workspaces
    post:
      consumes:
      - application/json
      parameters:
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/workspace.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/workspace.CreateRsp'
      security:
      - BasicAuth: []
      summary: Create workspace
      tags:
      - Workspaces
  /workspaces/{workspace_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete workspace
      tags:
      - Workspaces
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace_id
        in: path
        name: workspace_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/workspace.Workspace'
      security:
      - BasicAuth: []
      summary: Get workspace details
      tags:
      - Workspaces
  /workspaces/{workspace_id}/apis:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/api.Row'
            type: array
      security:
      - BasicAuth: []
      summary: List all api users
      tags:
      - API Users
    post:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/api.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/api.Row'
      security:
      - BasicAuth: []
      summary: Create api user
      tags:
      - API Users
  /workspaces/{workspace_id}/apis/{api_user_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: api_user_id
        in: path
        name: api_user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Remove api user
      tags:
      - API Users
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: api_user_id
        in: path
        name: api_user_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/api.Row'
      security:
      - BasicAuth: []
      summary: Get api user
      tags:
      - API Users
    patch:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: api_user_id
        in: path
        name: api_user_id
        required: true
        type: integer
      - description: body
        in: body
        name: PatchReq
        required: true
        schema:
          $ref: '#/definitions/api.PatchReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Partial update of API user information
      tags:
      - API Users
  /workspaces/{workspace_id}/groups:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/group.Rsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all groups
      tags:
      - Groups
    post:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/group.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/group.Rsp'
      security:
      - BasicAuth: []
      summary: Create group
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Delete group
      tags:
      - Groups
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: user id
        in: header
        name: sa-user-id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/group.Rsp'
      security:
      - BasicAuth: []
      summary: Get group info
      tags:
      - Groups
    put:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: body
        in: body
        name: UpdateReq
        required: true
        schema:
          $ref: '#/definitions/group.UpdateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/group.Rsp'
      security:
      - BasicAuth: []
      summary: Update the group
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}/apis:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/group.RowAPIUser'
            type: array
      security:
      - BasicAuth: []
      summary: Get group api users
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}/users:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/group.RowUser'
            type: array
      security:
      - BasicAuth: []
      summary: Get group users
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}/users/check:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - collectionFormat: csv
        description: filter by list of user id
        in: query
        items:
          type: integer
        name: ids
        required: true
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: integer
            type: array
      security:
      - BasicAuth: []
      summary: Check users in the group
      tags:
      - Groups
  /workspaces/{workspace_id}/groups/{group_id}/users2apis:
    put:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: group id
        in: path
        name: group_id
        required: true
        type: integer
      - description: body
        in: body
        name: '[]UpdateUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/group.UpdateUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user list
      tags:
      - Groups
  /workspaces/{workspace_id}/invites:
    post:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: CreateReq
        required: true
        schema:
          $ref: '#/definitions/invite.CreateReq'
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Create invite
      tags:
      - Invites
  /workspaces/{workspace_id}/invites/{login}:
    delete:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: login
        in: path
        name: login
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Remove from the workspace
      tags:
      - Invites
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: login
        in: path
        name: login
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/invite.Row'
      security:
      - BasicAuth: []
      summary: Get invite
      tags:
      - Invites
  /workspaces/{workspace_id}/roles/available:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        required: true
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: search query
        in: query
        name: search
        type: string
      - description: 'sort by field, available values: name'
        in: query
        name: sort_by
        type: string
      - description: order by asc or desc
        in: query
        name: order_by
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/role.AvailableRsp'
            type: array
      security:
      - BasicAuth: []
      summary: List all available roles
      tags:
      - Roles
  /workspaces/{workspace_id}/users:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: SA user id
        in: header
        name: sa-user-id
        type: string
      - description: limit for lazy loading
        in: query
        name: limit
        type: integer
      - description: offset for lazy loading
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/user.ShortRow'
            type: array
      security:
      - BasicAuth: []
      summary: List workspace users
      tags:
      - Users
    post:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: '[]AddReq'
        required: true
        schema:
          items:
            $ref: '#/definitions/user.AddReq'
          type: array
      produces:
      - application/json
      responses:
        "201":
          description: Created
      security:
      - BasicAuth: []
      summary: Add user to the workspace
      tags:
      - Users
    put:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: body
        in: body
        name: '[]UpdateUser'
        required: true
        schema:
          items:
            $ref: '#/definitions/user.UpdateUser'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Change user list in the workspace
      tags:
      - Users
  /workspaces/{workspace_id}/users/{user_id}:
    put:
      consumes:
      - application/json
      parameters:
      - description: user id
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: user_id
        in: path
        name: user_id
        required: true
        type: integer
      - description: body
        in: body
        name: Req
        required: true
        schema:
          $ref: '#/definitions/user.UpdateStatusReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Update user status
      tags:
      - Users
  /workspaces/{workspace_id}/users/{user_id}/perms/{type}/{perms}/verify:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: SA userID
        in: path
        name: user_id
        required: true
        type: string
      - description: scope type (sa/control)
        in: path
        name: type
        required: true
        type: string
      - description: perms (comma separated)
        in: path
        name: perms
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.VerifyRsp'
      security:
      - BasicAuth: []
      summary: Verify permissions
      tags:
      - Users
  /workspaces/{workspace_id}/users/check:
    get:
      consumes:
      - application/json
      parameters:
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - collectionFormat: csv
        description: filter by list of user id
        in: query
        items:
          type: integer
        name: ids
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: integer
            type: array
      security:
      - BasicAuth: []
      summary: Check users in the workspace
      tags:
      - Users
  /workspaces/{workspace_id}/users/search/{query}:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        type: string
      - description: workspace id
        in: path
        name: workspace_id
        required: true
        type: string
      - description: query
        in: path
        name: query
        required: true
        type: string
      - description: limit
        in: query
        name: limit
        required: true
        type: integer
      - description: offset
        in: query
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/user.ShortRow'
            type: array
      security:
      - BasicAuth: []
      summary: Search users by id/name/login
      tags:
      - Users
  /workspaces/search/{query}:
    get:
      consumes:
      - application/json
      parameters:
      - description: SA userID
        in: header
        name: sa-user-id
        required: true
        type: string
      - description: query
        in: path
        name: query
        required: true
        type: string
      - description: limit
        in: query
        name: limit
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/workspace.Row'
            type: array
      security:
      - BasicAuth: []
      summary: Search workspaces by ext_id/name/
      tags:
      - Workspaces
  /workspaces/sync:
    post:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
      security:
      - BasicAuth: []
      summary: Search
      tags:
      - Workspaces
securityDefinitions:
  BasicAuth:
    type: basic
swagger: "2.0"
