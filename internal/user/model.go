package user

import (
	"encoding/json"
	"sa/internal/authz"
	"time"
)

type Auth struct {
	Type               int    `json:"type" default:"1"`
	UserID             int    `json:"user_id" default:"1"`
	AccessToken        string `json:"access_token" default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	RefreshToken       string `json:"refresh_token" default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	AccessTokenExpire  int    `json:"access_token_expire" default:"15"`
	RefreshTokenExpire int    `json:"refresh_token_expire" default:"15"`
}

type AuthPOST struct {
	JWT string `json:"jwt" default:"eyJhbGciOiJIUzI1NiIsIn9.eyJhbGciOiJIUzI1NiIsIn9.eyJhbGciOiJIUzI1NiIsIn9"`
}

type AuthReq struct {
	ClientID int    `json:"client_id" default:"ee7c2b5b-5d47-4b6c-a127-d9d31fd84dda"`
	Scope    string `json:"scope" default:"read"`
	Auth
}

type CreateDBUserAPIRow struct {
	ResourceID int    `db:"resource_id"`
	URL        string `db:"url"`
	OwnerID    int    `db:"owner_user_id"`
	Secret     string `db:"secret"`
}

type CreateRow struct {
	UserName   string  `json:"user_name" default:"UserName"`
	UserData   *string `json:"user_data" default:"UserData"`
	Color      *string `json:"color"`
	UserLang   *string `json:"user_lang" default:"UserLang"`
	Login      string  `json:"login" default:"Login"`
	LoginType  int     `json:"login_type" default:"LoginType"`
	LoginHash1 *string `json:"login_hash1" default:"LoginHash1"`
}
type Short struct {
	ID             int              `db:"id"`
	Name           string           `db:"name"`
	Status         authz.StatusUser `db:"status"`
	Hash1          *string          `db:"hash1"`
	Login          string           `db:"login"`
	LoginID        int              `db:"login_id"`
	HashCreatedAt  *time.Time       `db:"hash_created_at"`
	LoginBlockedAt *time.Time       `db:"login_blocked_at"`
}

type Login struct {
	ID         int     `db:"id"`
	UserID     int     `db:"user_id"`
	CreateTime int     `db:"create_time"`
	Login      string  `db:"login"`
	Type       int     `db:"type"`
	Hash       *string `db:"hash1"`
}

type RowForEvent struct {
	ID   int    `json:"-" db:"id"`
	Name string `json:"name" db:"name"`
}

type SearchRow struct {
	ID    int     `db:"id"`
	Name  string  `db:"name"`
	URL   *string `db:"url"`
	Color *string `db:"color"`
	BaseRow
}

type Scope struct {
	ID   string `db:"id"`
	Type string `db:"type"`
}
type APIShortRow struct {
	ID          int              `json:"ID" db:"id"`
	Name        string           `json:"Name" db:"name"`
	Status      int              `json:"Status" db:"status"`
	Photo       *string          `json:"Photo,omitempty" db:"photo"`
	Color       *string          `json:"Color,omitempty" db:"color"`
	OwnerID     int              `json:"OwnerID" db:"owner_user_id"`
	URL         string           `json:"URL" db:"url"`
	WExtID      string           `json:"WExtID" db:"workspace_ext_id"`
	WID         int              `json:"WID" db:"workspace_id"`
	WName       string           `json:"WName" db:"workspace_name"`
	AccessToken string           `json:"AccessToken" db:"secret"`
	Scopes      map[int][]string `json:"Scopes,omitempty" db:"-"`
}

func (s APIShortRow) MarshalBinary() (data []byte, err error) {
	return json.Marshal(s)
}

type APIRow struct {
	ID        int        `db:"id"`
	OwnerID   int        `db:"owner_user_id"`
	Name      string     `db:"name"`
	Status    int        `db:"status"`
	Photo     *string    `db:"photo"`
	Color     *string    `db:"color"`
	OwnerName string     `db:"owner_name"`
	Secret    string     `db:"secret"`
	URL       string     `db:"url"`
	CreatedAt time.Time  `db:"created_at"`
	Meta      authz.META `db:"meta"`
}

type TokenRow struct {
	Login string `db:"login"`
	Token string `db:"token"`
	Type  int    `db:"type"`
}

type Row struct {
	ID    int     `db:"id"`
	Name  string  `db:"name"`
	URL   *string `db:"url"`
	Is2fa bool    `db:"is_2fa"`
	// CreateTime int     `db:"create_time"`
	LastEntrance    *int             `db:"last_entrance"`
	LastLogout      *int             `db:"last_logout"`
	CreateTime      int              `db:"create_time"`
	RuleStatus      *int             `db:"rule_status"`
	Color           *string          `db:"color"`
	Status          authz.StatusUser `db:"status"`
	UpdatedStatusAt *time.Time       `db:"updated_status_at"`
	CreatedAt       time.Time        `db:"created_at"`
	Type            int              `db:"type"`
	BaseRow
}

type BaseRow struct {
	Data  *string `db:"data" `
	Photo *string `db:"-"`
}

func (d *BaseRow) MarshalData() {
	data := make(map[string]any)
	if d.Data != nil {
		err := json.Unmarshal([]byte(*d.Data), &data)
		if err != nil {
			return
		}
		if photo, ok := data["photo"].(string); ok {
			d.Photo = &photo
		}
	}
}

type UserToken struct {
	ID          string    `db:"id" json:"id"`
	UserID      int       `db:"user_id" json:"user_id"`
	Name        string    `db:"name" json:"name"`
	Description *string   `db:"description" json:"description,omitempty"`
	Token       string    `db:"token" json:"token"`
	ExpireAt    time.Time `db:"expire_at" json:"expire_at"`
	Status      string    `db:"status" json:"status"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

type UserTokenCreateReq struct {
	Name        string    `json:"name"`
	Description *string   `json:"description,omitempty"`
	ExpireAt    time.Time `json:"expire_at"`
}

type UserTokenUpdateReq struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	Status      *string `json:"status,omitempty"`
}
