package user

import (
	"context"
	"crypto/sha1" //nolint:gosec // we need sha1
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log/slog"
	"math"
	"regexp"
	"sa/internal/license"
	"sa/internal/webhook"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"sa/pkg/ptrutil"

	"sa/apperr"
	"sa/internal/authz"
	"sa/internal/base"
	"sa/internal/cache"
	"sa/internal/event"
	"sa/internal/role"
	"sa/internal/scope"
	"sa/pkg/pgsql"
	"sa/pkg/rand"
)

type Status string

var (
	StatusActive  = Status("active")
	StatusBlocked = Status("blocked")
)

type Filter struct {
	RoleIDs []int
	Search  string
	Types   []int
	IDs     []int
}

type Service struct {
	*base.Base
	licenseService *license.Service
	scopeService   scope.Service
	roleService    *role.Service
	db             DB
}

func NewService(licenseService *license.Service, roleService *role.Service, scopeService scope.Service, bs *base.Base) *Service {
	return &Service{
		licenseService: licenseService,
		scopeService:   scopeService,
		roleService:    roleService,
		db:             NewStorage(),
		Base:           bs,
	}
}

func (s *Service) GetExtTokens(ctx context.Context, login []string, tType int) []TokenRow {
	return s.db.GetExtToken(ctx, login, tType)
}

func (s *Service) CreateExtToken(ctx context.Context, login string, tType int, token string) {
	s.db.CreateExtToken(ctx, login, tType, token)
}

type RG struct {
	WID    int
	Roles  map[string]Res
	Groups map[string]Res
}

func (s *Service) ListByName(ctx context.Context, names []string) map[string]RG {
	rsp := s.db.ListWSByName(ctx, names)
	for wName := range rsp {
		for rID, rName := range authz.RoleNames {
			rsp[wName].Roles[rName] = Res{
				ID:   rID,
				Type: authz.TypeRole,
			}
		}
	}
	return rsp
}

type UWI struct {
	Roles  map[int]string
	Groups map[int]string
}

func (s *Service) ListAllLinks(ctx context.Context, uid int) map[int]UWI {
	userLinks := s.db.ListAllLinks(ctx, uid)
	userWSs := make(map[int]UWI)
	for _, item := range userLinks {
		if _, ok := userWSs[item.WID]; !ok {
			userWSs[item.WID] = UWI{Roles: make(map[int]string), Groups: make(map[int]string)}
		}
		if item.Type == authz.TypeRole {
			userWSs[item.WID].Roles[item.ID] = item.Name
		} else {
			userWSs[item.WID].Groups[item.ID] = item.Name
		}
	}
	return userWSs
}

func (s *Service) GetUserAPI(ctx context.Context, id int) (APIShortRow, error) {
	rsp, err := s.Cache.With(cache.TypeAPIUser, strconv.Itoa(id)).GetWithUpdate(ctx, func() (interface{}, error) {
		r, err := s.db.GetUserAPI(ctx, id)
		return r, err
	}, func(v []byte) (any, error) {
		var rsp1 APIShortRow
		err := json.Unmarshal(v, &rsp1)
		return rsp1, err
	})
	if err != nil {
		return APIShortRow{}, err
	}
	return rsp.(APIShortRow), err
}

func (s *Service) GetUserAPIIDBySecret(ctx context.Context, secret string) (int, error) {
	rsp, err := s.Cache.With(cache.TypeAPIUserSecret, secret).GetWithUpdate(ctx, func() (interface{}, error) {
		return s.db.GetUserAPIIDBySecret(ctx, secret)
	}, func(v []byte) (any, error) {
		return strconv.Atoi(string(v))
	})
	if err != nil {
		return 0, err
	}
	return rsp.(int), err
}

func (s *Service) ListUserAPIScopes(ctx context.Context, ids []int) map[int][]Scope {
	return s.db.ListUserAPIScopes(ctx, ids)
}

func (s *Service) Update(ctx context.Context, id int, name string, photo, color, lang *string) error {

	err := s.AS.Apply(ctx, []authz.ChainElement{
		{
			Operation: authz.OperationUpdate,
			Data: authz.Resource{
				ID:         id,
				Type:       authz.TypeUser,
				Name:       name,
				Photo:      photo,
				Color:      color,
				Attributes: "{}",
			},
		},
	})
	if err != nil {
		return fmt.Errorf("failed to apply update: %w", err)
	}

	ui, err := s.db.GetByID(ctx, id)
	if err != nil {
		return err
	}
	rightPhotoURL := photo
	if photo != nil && *photo != "" {
		rightPhotoURL = ptrutil.Ptr(fmt.Sprintf("%s/avatars/%d.jpg", s.Cfg.URL, id))

	}
	err = s.db.Update(ctx, id, name, rightPhotoURL, lang)
	if err != nil {
		return err
	}
	data := event.UserSet{
		ID:     id,
		Name:   name,
		Status: ui.Status.String(),
		Photo:  photo,
		Color:  color,
		Lang:   lang,
	}
	s.Base.ES.Push(ctx, 0, event.NameUserSet, data)
	s.Base.Cache.With(cache.TypeUser, strconv.Itoa(ui.ID)).Del(ctx) // invalidate user cache
	err = s.AS.StoreAvatar(ctx, photo, authz.TypeUser, id, false)
	if err != nil {
		return fmt.Errorf("s.AS.StoreAvatar: %w", err)
	}

	// invalidate user cache one more time
	pgsql.OnCommit(ctx, func(ctx context.Context) error {
		s.Base.Cache.With(cache.TypeUser, strconv.Itoa(id)).Del(ctx)
		return nil
	})

	return nil

}

func (s *Service) UpdateAPIUser(ctx context.Context, uid, wid, apiUserID int, req CreateUpdateAPIRow) error {

	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermAllAPIManaging); err == nil {
		return s.updateAPIUser(ctx, wid, apiUserID, req)
	}
	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermAPIManaging); err != nil {
		return apperr.ErrNoPermissions
	}
	ai, err := s.db.GetUserAPI(ctx, apiUserID)
	apperr.Error(err, "api user not found")
	if ai.OwnerID == uid {
		return s.updateAPIUser(ctx, wid, apiUserID, req)
	}
	return apperr.ErrNoPermissions
}

func (s *Service) updateAPIUser(ctx context.Context, wid, apiUserID int, req CreateUpdateAPIRow) error {
	s.Cache.With(cache.TypeAPIUser, strconv.Itoa(apiUserID)).Del(ctx)

	err := s.db.UpdateAPIUserInfo(ctx, apiUserID, req)
	if err != nil {
		return fmt.Errorf("s.db.UpdateAPIUserInfo: %w", err)
	}
	scopesChain, err := s.prepareScopesRules(ctx, wid, apiUserID, req.Scopes)
	if err != nil {
		return fmt.Errorf("s.prepareScopesRules: %w", err)
	}
	err = s.Base.AS.Apply(ctx, scopesChain)
	if err != nil {
		return err
	}

	s.sendAPIEvent(ctx, wid, apiUserID, req, req.Status, "updated")
	s.Cache.With(cache.TypeAPIUser, strconv.Itoa(apiUserID)).Del(ctx)

	err = s.AS.StoreAvatar(ctx, req.Photo, authz.TypeAPIUser, apiUserID, true)
	if err != nil {
		return fmt.Errorf("s.AS.StoreAvatar: %w", err)
	}
	pgsql.OnCommit(ctx, func(ctx context.Context) error {
		s.Cache.With(cache.TypeAPIUser, strconv.Itoa(apiUserID)).Del(ctx)
		return nil
	})
	return nil
}

func (s *Service) sendAPIEvent(ctx context.Context, wid, apiUserID int, req CreateUpdateAPIRow, status int, action string) {
	allScopes := s.db.ListUserAPIScopesMap(ctx, apiUserID)
	allScopes1 := make([]event.WSAPIUserSetItem, 0)
	allWHScopes := make([]webhook.APIKeyItem, 0)
	for scopeTypeInt, scopeList := range allScopes {
		var scopeType string
		switch scopeTypeInt {
		case authz.TypeScopeCorezoid:
			scopeType = "corezoid"
		case authz.TypeScopeControl:
			scopeType = "control"
		default:
			scopeType = "account"
		}
		allScopes1 = append(allScopes1, event.WSAPIUserSetItem{
			Type: scopeType,
			IDs:  scopeList,
		})
		allWHScopes = append(allWHScopes, webhook.APIKeyItem{
			Type: scopeType,
			IDs:  scopeList,
		})
	}
	s.Base.ES.Push(ctx, wid, event.NameWsAPIUserSet, event.WSAPIUserSet{
		ID:     apiUserID,
		Name:   req.Name,
		Photo:  req.Photo,
		Color:  req.Color,
		URL:    req.URL,
		Scopes: allScopes1,
	})
	statusBin := "active"
	if status == 1 {
		statusBin = "blocked"
	}
	s.Base.WHS.Push(ctx, wid, webhook.NameAPIKey, webhook.APIKey{
		ID:     apiUserID,
		Name:   req.Name,
		Photo:  req.Photo,
		Color:  req.Color,
		URL:    req.URL,
		Scopes: allWHScopes,
		Status: statusBin,
		Action: action,
	})
}

func (s *Service) ListUserAPIs(ctx context.Context, uid, wid int, f Filter, limit, offset int, sortBy, orderBy string) []*APIRow {
	if uid == 0 {
		return s.db.ListUserAPIs(ctx, wid, 0, f.Search, f.IDs, limit, offset, sortBy, orderBy)
	}
	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermAPIManaging, authz.PermAllAPIManaging); err != nil {
		return []*APIRow{}
	}
	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermAllAPIManaging); err == nil {
		return s.db.ListUserAPIs(ctx, wid, 0, f.Search, f.IDs, limit, offset, sortBy, orderBy)
	}
	return s.db.ListUserAPIs(ctx, wid, uid, f.Search, f.IDs, limit, offset, sortBy, orderBy)
}

func (s *Service) DeleteAPIUser(ctx context.Context, uid, wid, id int) error {
	ai, err := s.db.GetUserAPI(ctx, id)
	apperr.Error(err, "api user not found")
	sendWHS := func() {
		allWHScopes := make([]webhook.APIKeyItem, 0)
		for scopeTypeInt, scopeList := range ai.Scopes {
			scopeType := "control"
			if scopeTypeInt == authz.TypeScopeCorezoid {
				scopeType = "corezoid"
			}
			allWHScopes = append(allWHScopes, webhook.APIKeyItem{
				Type: scopeType,
				IDs:  scopeList,
			})
		}
		status := "active"
		if ai.Status == 1 {
			status = "blocked"
		}
		s.Base.WHS.Push(ctx, wid, webhook.NameAPIKey, webhook.APIKey{
			ID:     ai.ID,
			Name:   ai.Name,
			Photo:  ai.Photo,
			Color:  ai.Color,
			URL:    ai.URL,
			Scopes: allWHScopes,
			Status: status,
			Action: "deleted",
		})
	}
	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermAllAPIManaging); err == nil {
		sendWHS()
		return s.deleteAPIUser(ctx, wid, id)
	}
	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermAPIManaging); err != nil {
		return apperr.ErrNoPermissions
	}

	if ai.OwnerID == uid {
		sendWHS()
		return s.deleteAPIUser(ctx, wid, id)
	}
	return apperr.ErrNoPermissions
}

func (s *Service) deleteAPIUser(ctx context.Context, wid, id int) error {
	chain := []authz.ChainElement{
		{
			Operation: authz.OperationDelete,
			Data: authz.Resource{
				ID:   id,
				Type: authz.TypeAPIUser,
			},
		},
	}

	err := s.Base.AS.Apply(ctx, chain)
	if err != nil {
		return fmt.Errorf("s.Base.AS.Apply: %w", err)
	}
	err = s.db.DeleteUser(ctx, id)
	if err != nil {
		return fmt.Errorf("s.db.DeleteUser: %w", err)
	}
	pgsql.OnCommit(ctx, func(ctx context.Context) error {
		s.Cache.With(cache.TypeAPIUser, strconv.Itoa(id)).Del(ctx)
		s.Cache.With(cache.TypeWorkspaceUsers, strconv.Itoa(wid)).HDel(ctx, id)
		return nil
	})

	return nil
}

type CreateUpdateAPIRow struct {
	Name  string
	Photo *string
	Color *string
	URL   string
	// specified only new ones
	Scopes []Scope
	Secret string
	Status int
	Meta   *authz.META
}

func (s *Service) CreateAPIUser(ctx context.Context, uid, wid int, req CreateUpdateAPIRow) (int, error) {
	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermAPIManaging, authz.PermAllAPIManaging); err != nil {
		return 0, apperr.ErrNoPermissions
	}
	if req.Secret == "" {
		req.Secret = "atn_" + rand.Seq(32)
	}
	id, err := s.CreateAPIUserNoValidate(ctx, uid, wid, 0, req)
	if err != nil {
		return 0, fmt.Errorf("s.CreateAPIUserNoValidate: %w", err)
	}
	apiUID := id
	s.roleService.SetUserInCache(ctx, wid, []int{apiUID})
	err = s.AS.StoreAvatar(ctx, req.Photo, authz.TypeAPIUser, apiUID, true)
	if err != nil {
		return 0, fmt.Errorf("s.AS.StoreAvatar: %w", err)
	}
	return apiUID, nil
}

func (s *Service) CreateAPIUserNoValidate(ctx context.Context, ownerID, wid, apiUserID int, req CreateUpdateAPIRow) (int, error) {
	var chain []authz.ChainElement
	var data *string
	if req.Photo != nil {
		d := fmt.Sprintf(`{"photo":%q}`, *req.Photo)
		data = &d
	}
	if apiUserID == 0 {
		apiUserID = s.db.CreateJustUser(ctx, req.Name, data, "en")
	}

	apiUser := authz.Resource{
		ID:          apiUserID,
		WorkspaceID: &wid,
		Type:        authz.TypeAPIUser,
		Name:        req.Name,
		Photo:       req.Photo,
		Color:       req.Color,
		Attributes:  "{}",
		Meta:        req.Meta,
	}
	chain = append(chain, authz.ChainElement{Operation: authz.OperationInsert, Data: apiUser})
	scopesChain, err := s.prepareScopesRules(ctx, wid, apiUserID, req.Scopes)
	if err != nil {
		return 0, err
	}
	chain = append(chain, scopesChain...)
	err = s.AS.Apply(ctx, chain)
	if err != nil {
		return 0, err
	}
	err = s.db.CreateUserAPIInfo(ctx, CreateDBUserAPIRow{
		ResourceID: apiUserID,
		URL:        req.URL,
		OwnerID:    ownerID,
		Secret:     req.Secret,
	})
	apperr.Error(err)
	s.sendAPIEvent(ctx, wid, apiUserID, req, 0, "added")
	return apiUserID, err
}

func (s *Service) prepareScopesRules(ctx context.Context, wid, apiUserID int, scopes []Scope) ([]authz.ChainElement, error) {
	chain := make([]authz.ChainElement, 0)
	allScopes := s.scopeService.List(ctx, authz.TypeScopeControl, authz.TypeScopeCorezoid, authz.TypeScopesSA)
	for _, scope := range scopes {
		stype := authz.GetResourcesByName()[scope.Type]
		if stype != authz.TypeScopeControl && stype != authz.TypeScopeCorezoid && stype != authz.TypeScopesSA {
			return chain, apperr.New(apperr.ErrBadRequest, "wrong scopeType")
		}
		sid := 0
		for _, item := range allScopes {
			if item.ExtID == scope.ID && item.Type == stype {
				sid = item.ID
				break
			}
		}
		if sid == 0 {
			return []authz.ChainElement{}, apperr.New(apperr.ErrBadRequest, "wrong scopeID")
		}
		rule := authz.Rule{
			WID:   &wid,
			SType: authz.TypeAPIUser,
			SID:   apiUserID,
			OType: stype,
			OID:   &sid,
			Rel:   authz.RelInclude,
		}
		e := authz.ChainElement{Operation: authz.OperationInsert, Data: rule}
		chain = append(chain, e)
	}
	return chain, nil
}

type AddRow struct {
	UserID int `json:"user_id"`
	RoleID int `json:"role_id"`
}

func (s *Service) Search(ctx context.Context, uid, wid int, types []int, searchText string, limit, offset int) []SearchRow {
	if uid > 0 {
		if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermViewUsers); err != nil {
			return []SearchRow{}
		}
	}
	return s.db.Search(ctx, wid, types, pgsql.ToTSV(searchText), limit, offset)
}

func (s *Service) Get(ctx context.Context, uid, wid, id int) (Row, error) {
	if uid > 0 && uid != id {
		if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermViewUsers); err != nil {
			return Row{}, apperr.New(apperr.ErrNoPermissions)
		}
	}
	rsp, _ := s.db.List(ctx, Filter{IDs: []int{id}}, wid, 0, 0, "", "")
	if len(rsp) > 0 {
		return *rsp[0], nil
	}
	return Row{}, apperr.ErrUserNotFound
}

func (s *Service) List(ctx context.Context, uid int, f Filter, wid, limit, offset int, sortBy, orderBy string) (rsp []*Row, total int) {
	if uid > 0 {
		if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermViewUsers); err != nil {
			return []*Row{}, 0
		}
	}
	// TODO: remove blocked users
	return s.db.List(ctx, f, wid, limit, offset, sortBy, orderBy)
}
func (s *Service) ListAll(ctx context.Context, search string, status *authz.StatusUser, limit, offset int, sortBy, orderBy string) (users []RowAll, total int) {
	return s.db.ListAll(ctx, search, status, limit, offset, sortBy, orderBy)
}
func (s *Service) ListByLicense(ctx context.Context, ownerID int, f Filter, limit, offset int, sortBy, orderBy string) (users []*Row, total int) {
	return s.db.ListByLicense(ctx, ownerID, f, limit, offset, sortBy, orderBy)
}

func (s *Service) ListByID(ctx context.Context, ids []int) ([]RowForEvent, error) {
	return s.db.ListByID(ctx, ids)
}

func (s *Service) GetByID(ctx context.Context, id int) (Row, error) {
	return s.db.GetByID(ctx, id)
}

func (s *Service) IsValidLogin(sType int, login string) bool {
	switch sType {
	case authz.AuthTypeGoogle, authz.AuthTypeSimpleEmail:
		pattern := `^(|(([A-Za-z0-9]+_+)|([A-Za-z0-9]+\-+)|([A-Za-z0-9]+\.+)|([A-Za-z0-9]+\++))*[A-Za-z0-9]+@((\w+\-+)|(\w+\.))*\w{1,63}\.[a-zA-Z]{2,12})$`
		return regexp.MustCompile(pattern).MatchString(login)
	}
	return true
}

func (s *Service) Create(ctx context.Context, req CreateRow) (lid, uid int) {
	if !s.IsValidLogin(req.LoginType, req.Login) {
		panic(apperr.New(apperr.ErrBadRequest, "invalid email login"))
	}
	lid, uid = s.db.CreateUser(ctx, req)
	var photo *string
	if req.UserData != nil && *req.UserData != "" {
		ud := make(map[string]any)
		if err := json.Unmarshal([]byte(*req.UserData), &ud); err == nil {
			if v, ok := ud["photo"]; ok {
				photo = ptrutil.Ptr(v.(string))
			}
		}
	}
	err := s.AS.Apply(ctx, []authz.ChainElement{{
		Operation: authz.OperationInsert,
		Data: authz.Resource{
			ID:         uid,
			Name:       req.UserName,
			Type:       authz.TypeUser,
			Attributes: "{}",
			Photo:      photo,
			Color:      req.Color,
		},
	}})
	apperr.Error(err)
	return lid, uid
}

func (s *Service) GetIDsByDomain(ctx context.Context, domain string) []int {
	return s.db.GetIDsByDomain(ctx, domain)
}

func (s *Service) GetUsersByLogin(ctx context.Context, lType int, login string) []Short {
	return s.db.GetUsersByLogin(ctx, lType, login)
}

func (s *Service) BlockLogin(ctx context.Context, loginID int) {
	s.db.BlockLogin(ctx, loginID)
}

func (s *Service) GetByToken(ctx context.Context, token string, clientID int) (Row, error) {
	return s.db.GetByToken(ctx, token, clientID)
}

func (s *Service) GetLogins(ctx context.Context, userID []int) ([]Login, error) {
	return s.db.GetLogins(ctx, userID)
}

func (s *Service) Block(ctx context.Context, uid, wid, id int) error {
	if uid > 0 {
		err := s.CheckBlockAction(ctx, uid, wid, id)
		if err != nil {
			return err
		}
	}
	ui, err := s.Get(ctx, 0, wid, id)
	if err != nil {
		return err
	}
	if ui.RuleStatus != nil {
		return apperr.New(apperr.ErrAlreadyBlocked)
	}
	roleRule := authz.Rule{
		WID:   &wid,
		SType: authz.TypeUser,
		SID:   id,
		OType: authz.TypeWorkspace,
		Rel:   authz.RelBlocked,
	}

	chain := []authz.ChainElement{{Operation: authz.OperationInsert, Data: roleRule}}
	err = s.Base.AS.Apply(ctx, chain)
	if err != nil {
		return fmt.Errorf("apply: %w", err)
	}
	usersPerms := s.roleService.ListPermsByUserInWSString(ctx, wid, []int{ui.ID})
	s.Base.ES.Push(ctx, wid, event.NameWsUserSet, event.WSUserSet{
		Users: []event.WSUserSetItem{{
			ID:          ui.ID,
			Name:        ui.Name,
			Status:      string(authz.StatusUserInWSBlocked),
			ObjOwnerIDs: make([]event.WSUserSetItemObjOwnerID, 0),
			Photo:       ui.Photo,
			Color:       ui.Color,
			AuthorID:    &uid,
			Perms:       usersPerms[ui.ID],
			Active:      true,
		}},
	})

	s.WHS.Push(ctx, wid, webhook.NameUser, webhook.User{
		Users: []webhook.UserItem{
			{
				ID:     ui.ID,
				Status: string(authz.StatusUserInWSBlocked),
				Action: "updated",
			},
		},
	})
	s.roleService.DeleteOwnerRequestsByWSUser(ctx, wid, ui.ID)
	return err
}

func (s *Service) UnBlock(ctx context.Context, uid, wid, id int) error {
	// todo rewrite with tx
	if s.License.CanBeExceeded(ctx, s.License.Owner(ctx, wid), []int{id}, true) {
		return apperr.ErrLicenseUsersExceeded
	}
	if uid > 0 {
		err := s.CheckBlockAction(ctx, uid, wid, id)
		if err != nil {
			return err
		}
	}

	ui, err := s.Get(ctx, 0, wid, id)
	if err != nil {
		return err
	}
	if ui.RuleStatus == nil {
		return apperr.New(apperr.ErrAlreadyActive)
	}
	roleRule := authz.Rule{
		WID:   &wid,
		SType: authz.TypeUser,
		SID:   id,
		OType: authz.TypeWorkspace,
		Rel:   authz.RelBlocked,
	}
	chain := []authz.ChainElement{{Operation: authz.OperationDelete, Data: roleRule}}
	err = s.Base.AS.Apply(ctx, chain)
	if err != nil {
		return err
	}
	usersPerms := s.roleService.ListPermsByUserInWSString(ctx, wid, []int{ui.ID})
	s.Base.ES.Push(ctx, wid, event.NameWsUserSet, event.WSUserSet{
		Users: []event.WSUserSetItem{{
			ID:          ui.ID,
			Name:        ui.Name,
			Status:      string(authz.StatusUserInWSActive),
			ObjOwnerIDs: make([]event.WSUserSetItemObjOwnerID, 0),
			Photo:       ui.Photo,
			Perms:       usersPerms[ui.ID],
			Color:       ui.Color,
			AuthorID:    &uid,
			Active:      true,
		}},
	})
	s.WHS.Push(ctx, wid, webhook.NameUser, webhook.User{
		Users: []webhook.UserItem{
			{
				ID:     ui.ID,
				Status: string(authz.StatusUserInWSActive),
				Action: "updated",
			},
		},
	})

	return err
}

func (s *Service) CheckBlockAction(ctx context.Context, uid, wid, id int) error {
	if err := s.Base.AS.MustHave(ctx, uid, wid, authz.TypePermSA, authz.PermBlockUsers); err != nil {
		return err
	}
	if uid == id {
		return apperr.New(apperr.ErrNoPermissions, "not allowed to block yourself")
	}
	rInfo := s.roleService.ListByUserWS(ctx, wid, []int{uid, id})
	uidRole := math.MaxInt32
	idRole := math.MaxInt32
	for _, r := range rInfo {
		if r.UID == uid && uidRole > r.RID {
			uidRole = r.RID
		}
		if r.UID == id && idRole > r.RID {
			idRole = r.RID
		}
	}
	if idRole == authz.RoleOwner && uidRole != authz.RoleOwner {
		return apperr.New(apperr.ErrNoPermissions, "not allowed to block owners")
	}

	return nil
}

func (s *Service) UpdateCompanyInfo(ctx context.Context, uid int, name, size string) error {

	return s.db.UpdateCompanyInfo(ctx, uid, name, size)
}

func (s *Service) UpdateStatus(ctx context.Context, status authz.StatusUser, users []*Row) error {
	ids := make([]int, len(users))
	for i := range users {
		ids[i] = users[i].ID
	}
	err := s.db.updateUsersStatus(ctx, status, ids)
	if err != nil {
		return fmt.Errorf("update status: %w", err)
	}
	for i := range users {
		user := users[i]
		userSet := event.UserSet{
			ID:     user.ID,
			Name:   user.Name,
			Status: status.String(),
			Photo:  user.Photo,
			Color:  user.Color,
		}
		s.Base.ES.Push(ctx, 0, event.NameUserSet, userSet)
	}
	if status == authz.StatusUserBlocked && s.Cfg.Type == "cloud" {
		for _, u := range users {
			pgsql.OnCommit(ctx, func(ctx context.Context) error {
				li, err := s.licenseService.GetCloud(ctx, u.ID)
				if err == nil {
					_ = s.licenseService.Cancel(ctx, u.ID, li.ID, license.ReasonSystem, nil)
				}
				return nil
			})
		}
	}

	return nil
}

func (s *Service) UpdateAPIOwner(ctx context.Context, wid, oldOwnerID, newOwnerID int) {
	s.db.updateAPIOwner(ctx, wid, oldOwnerID, newOwnerID)
}
func (s *Service) Logout(ctx context.Context, uid int) {
	s.db.setLastLogout(ctx, uid)
	s.db.deleteAllTokens(ctx, uid)
	s.Base.ES.Push(ctx, 0, event.NameUserLogout, event.UserLogout{UserID: uid})
}

func (s *Service) CreateOrGetUser(ctx context.Context, lType int, name, photo, login, password, hash, lang, provider string) int {
	userInfos := s.GetUsersByLogin(ctx, lType, login)
	if len(userInfos) > 1 {
		panic("something went wrong, more than one user with the same login")
	}
	if len(userInfos) == 0 {
		// new user
		// TODO: auth_utils:check_domain_is_not_forbidden
		// trying to merge logins
		ui, associated := s.maybeMergeUserLogins(ctx, lType, login, password, hash)
		if !associated {
			sReq := CreateRow{
				UserName:   name,
				UserLang:   ptrutil.Ptr(lang),
				Login:      login,
				LoginType:  lType,
				LoginHash1: s.generateHash(lType, login, password, hash),
			}
			if photo != "" {
				sReq.UserData = ptrutil.Ptr(fmt.Sprintf(`{"photo":%q}`, photo))
			}
			_, uid := s.Create(ctx, sReq)
			apperr.Error(s.Update(ctx, uid, name, &photo, nil, nil))
			data := event.UserReg{
				Login:    login,
				Nick:     name,
				Provider: provider,
			}
			s.Base.ES.Push(ctx, 0, event.NameUserReg, data)
			return uid
		}

		return ui.ID
	}
	// existing user
	ui := userInfos[0]
	return ui.ID
}

func (s *Service) maybeMergeUserLogins(ctx context.Context, lType int, login, password, hash string) (Short, bool) {
	switch lType {
	case authz.AuthTypeSimpleEmail:
		ul := s.GetUsersByLogin(ctx, authz.AuthTypeGoogle, login)
		if len(ul) == 0 {
			return Short{}, false
		}
		s.BindLoginToUser(ctx, ul[0].ID, lType, login, password, hash)
		return ul[0], true
	case authz.AuthTypeGoogle:
		ul := s.GetUsersByLogin(ctx, authz.AuthTypeSimpleEmail, login)
		if len(ul) == 0 {
			return Short{}, false
		}
		s.BindLoginToUser(ctx, ul[0].ID, lType, login, password, hash)
		return ul[0], true
	default:
		return Short{}, false
	}
}

func (s *Service) generateHash(lType int, login, pass, hash string) *string {
	if hash != "" {
		return &hash
	}
	var loginHash1 *string
	if lType == authz.AuthTypeSimpleEmail {
		loginHash1 = ptrutil.Ptr(s.GetPasswordHash(login, pass))
	}
	if lType == authz.AuthTypeAPI {
		loginHash1 = &pass
	}
	return loginHash1
}

func (s *Service) BindLoginToUser(ctx context.Context, userID, lType int, login, pass, hash string) {
	if !s.IsValidLogin(lType, login) {
		panic(apperr.New(apperr.ErrBadRequest, "invalid login"))
	}
	passHash := s.generateHash(lType, login, pass, hash)
	lid := s.db.bindLoginToUser(ctx, userID, lType, login, passHash)
	s.db.createPasswordHistory(ctx, lid, lType, passHash)
	pgsql.OnCommit(ctx, func(ctx context.Context) error {
		s.Base.Cache.With(cache.TypeUser, strconv.Itoa(userID)).Del(ctx)
		return nil
	})
}

func (s *Service) UpdateUserPassword(ctx context.Context, login string, lType int, pass string) error {
	newUserPassHash := s.GetPasswordHash(login, pass)
	li := s.GetUsersByLogin(ctx, lType, login)
	if len(li) == 0 {
		return apperr.ErrUserNotFound
	}
	passwords := s.db.GetPasswordsByLogin(ctx, li[0].LoginID)
	if slices.Contains(passwords, newUserPassHash) {
		return apperr.ErrDuplicatePassword
	}
	s.db.updateUserPassword(ctx, login, lType, newUserPassHash)
	s.db.createPasswordHistory(ctx, li[0].LoginID, lType, &newUserPassHash)
	return nil
}

func (s *Service) UpdateHashCreatedAt(ctx context.Context, loginID int, hashCreatedAt time.Time) {
	s.db.updateHashCreatedAt(ctx, loginID, hashCreatedAt)
}

func (s *Service) ValidDomain(login string) bool {
	if len(s.Cfg.AllowedDomains) > 0 {
		domain := strings.Split(login, "@")
		if len(domain) > 1 && !slices.Contains(s.Cfg.AllowedDomains, domain[1]) {
			return false
		}
		return true
	}
	if len(s.Cfg.ForbiddenDomainsRe) > 0 {
		domain := strings.Split(login, "@")
		if len(domain) < 2 {
			return true
		}
		for _, reStr := range s.Cfg.ForbiddenDomainsRe {
			re, err := regexp.Compile(reStr)
			apperr.Error(err, "regexp compile error: "+reStr)
			if re.MatchString(domain[1]) {
				return false
			}
		}
	}
	return true
}

func (s *Service) GetPasswordHash(login, pass string) string {
	algo := s.Cfg.LoginPassAuth.PasswordSaltAlgorithm
	salt := s.Cfg.LoginPassAuth.PasswordSalt
	data := []byte(login + salt + pass)
	switch algo {
	case "sha256":
		// Calculate the SHA-256 hash of the combined data
		hash := sha256.Sum256(data)
		hashHex := hex.EncodeToString(hash[:])
		return hashHex
	case "sha":
		// Calculate the SHA-1 hash of the combined data

		hash := sha1.Sum(data) //nolint:gosec // we need sha1
		hashHex := hex.EncodeToString(hash[:])
		return hashHex
	default:
		panic("unknown password salt algorithm")
	}

}

func (s *Service) UpdateLastEntranceDateAndLang(ctx context.Context, uid int, lang string) {
	s.db.updateLastEntranceDateAndLang(ctx, uid, lang)
}

func (s *Service) UnblockLogin(ctx *gin.Context, id int) {
	s.db.unblockLogin(ctx, id)
}

func (s *Service) UpdateStatusUserByLicence(ctx context.Context, ownerID, userID int, status string) error {
	usersToWS := s.licenseService.Users(ctx, ownerID)
	switch status {
	case "active":
		for _, w := range usersToWS[userID] {
			err := s.UnBlock(ctx, ownerID, w, userID)
			if err != nil {
				return fmt.Errorf("s.us.UnBlock with user id: %d: error: %w", userID, err)
			}
		}
	case "block":
		for _, w := range usersToWS[userID] {
			err := s.Block(ctx, ownerID, w, userID)
			if err != nil {
				return fmt.Errorf("s.us.Block with user id: %d: error: %w", userID, err)
			}
		}
	default:
		return apperr.New(apperr.ErrBadRequest, "wrong status")
	}

	return nil
}

func (s *Service) SetOnline(ctx context.Context, usersIDs []int) {
	for _, id := range usersIDs {
		s.Cache.With(cache.TypeSession, strconv.Itoa(id)).HMSet(ctx, "iat", time.Now().Unix())
	}
}

func (s *Service) GetSessionTimestamp(ctx context.Context, uid int) (exp int, active bool, err error) {
	ttlInfo, err := s.Cache.With(cache.TypeSession, strconv.Itoa(uid)).HMGet(ctx, "ttl", "iat")
	if err != nil || len(ttlInfo) != 2 {
		return 0, false, fmt.Errorf("s.Cache.HMGet: %w", err)
	}
	if len(ttlInfo) != 2 {
		return 0, false, fmt.Errorf("s.Cache.HMGet: wrong format %w", apperr.ErrNotFound)
	}
	var ttl int
	if ttlBin, ok := ttlInfo[0].(string); ok {
		ttl, _ = strconv.Atoi(ttlBin)
	}
	if ttl == 0 {
		ttl = s.Cfg.SettingInt("capi_cookie_expr_time", 3600*24*30)
		slog.Error("", "err", "strange situation, ttl is 0", "uid", uid)
	}

	iatUnix, err := strconv.Atoi(ttlInfo[1].(string))
	apperr.Error(err)
	iat := time.Unix(int64(iatUnix), 0)
	exp = int(iat.Add(time.Duration(ttl) * time.Second).Unix())
	if iat.Add(time.Duration(ttl) * time.Second).Before(time.Now()) {
		return 0, false, nil
	}
	return exp, true, nil
}

func (s *Service) ListUserTokens(ctx context.Context, uid int) ([]*UserToken, error) {
	return s.db.ListUserTokens(ctx, uid)
}

func (s *Service) CreateUserToken(ctx context.Context, uid int, req UserTokenCreateReq) (*UserToken, error) {
	token := "utn_" + rand.Seq(32, rand.Letters, rand.Digits, rand.CapitalLetters)
	return s.db.CreateUserToken(ctx, uid, req, token)
}

func (s *Service) UpdateUserToken(ctx context.Context, uid int, tokenID string, req UserTokenUpdateReq) (*UserToken, error) {
	if req.Status != nil && *req.Status != "revoked" {
		return nil, apperr.New(apperr.ErrBadRequest, "only status transition from active to revoked is allowed")
	}
	return s.db.UpdateUserToken(ctx, tokenID, uid, req)
}
