package user

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"sort"
	"strings"
	"time"

	"sa/pkg/pgsql"

	"github.com/lib/pq"

	"sa/apperr"
	"sa/internal/authz"
	"sa/pkg/ptrutil"
)

type DB struct {
}

func NewStorage() DB {
	return DB{}
}

type RowLink struct {
	ID   int
	WID  int
	Type int
	Name string
}
type Res struct {
	ID   int
	Type int
}

func (db *DB) ListWSByName(ctx context.Context, names []string) map[string]RG {
	conn := pgsql.FromContext(ctx)
	req := struct {
		Names any `db:"names"`
		authz.DBArgs
	}{}
	req.DBArgs = authz.NewDBArgs()
	req.Names = pq.Array(names)
	rsp := make(map[string]RG)
	query := `select  rsw.id, rsw.name, rsrg.type, rsrg.id, rsrg.name
			  from resources rsw 
			      left outer join resources rsrg on (rsrg.workspace_id = rsw.id and (rsrg.type = :type_role or rsrg.type = :type_group))
		where rsw.name = ANY(:names) and  rsw.type = :type_workspace 
group by rsw.id, rsw.name, rsrg.type, rsrg.id, rsrg.name
	
	`
	rows, err := conn.NamedQuery(query, req)

	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		wName := ""
		var rType *int
		var rID *int
		var rName *string
		wID := 0
		err := rows.Scan(&wID, &wName, &rType, &rID, &rName)
		apperr.Error(err)
		if _, ok := rsp[wName]; !ok {
			rsp[wName] = RG{
				WID:    wID,
				Roles:  make(map[string]Res),
				Groups: make(map[string]Res),
			}
		}

		if rName != nil && rType != nil {
			if *rType == authz.TypeRole {
				rsp[wName].Roles[*rName] = Res{ID: *rID, Type: *rType}
			} else if *rType == authz.TypeGroup {
				rsp[wName].Groups[*rName] = Res{ID: *rID, Type: *rType}
			}

		}
	}
	return rsp
}

func (db *DB) GetExtToken(ctx context.Context, login []string, tType int) []TokenRow {
	conn := pgsql.FromContext(ctx)
	query := `SELECT login, type, token FROM ext_tokens WHERE login = ANY($1) AND type = $2`
	rows, err := conn.QueryContext(ctx, query, pq.Array(login), tType)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	rsp := make([]TokenRow, 0)
	for rows.Next() {
		var row TokenRow
		err := rows.Scan(&row.Login, &row.Type, &row.Token)
		apperr.Error(err)
		rsp = append(rsp, row)
	}
	return rsp
}

func (db *DB) CreateExtToken(ctx context.Context, login string, tType int, token string) {
	conn := pgsql.FromContext(ctx)
	query := `INSERT INTO ext_tokens (login, type, token, created_at) 
				VALUES ($1, $2, $3, $4) ON CONFLICT (type, login) DO UPDATE SET token = $3, created_at = $4`
	_, err := conn.ExecContext(ctx, query, login, tType, token, time.Now())
	apperr.Error(err)
}

func (db *DB) ListAllLinks(ctx context.Context, uid int) []RowLink {
	conn := pgsql.FromContext(ctx)
	req := struct {
		UID int `db:"user_id"`
		authz.DBArgs
	}{}
	req.DBArgs = authz.NewDBArgs()
	req.UID = uid
	rsp := make([]RowLink, 0)
	query := `select rs.id, rlu.workspace_id, rs.type, rs.name
              from resources rs 
                inner join rules rlu on  ( 
				    rlu.object_id = rs.id and
				    rs.type = rlu.object_type)
               inner join resources rw on (rw.id = rlu.workspace_id and rw.type = :type_workspace and rw.status != 2)
           	where  
           	      rlu.relation=:rel_member and
           	      rlu.subject_type =:type_user and
           	      rlu.subject_id = :user_id  and 
           	      (rs.type=:type_role or rs.type=:type_group)
           	    
          GROUP BY rs.id, rlu.workspace_id, rs.type, rs.name`

	rows, err := conn.NamedQuery(query, req)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		item := RowLink{}
		err := rows.Scan(&item.ID, &item.WID, &item.Type, &item.Name)
		apperr.Error(err)
		rsp = append(rsp, item)
	}

	return rsp
}

func (db *DB) DeleteUser(ctx context.Context, id int) error {
	conn := pgsql.FromContext(ctx)
	query := `DELETE FROM users WHERE id = $1`
	_, err := conn.ExecContext(ctx, query, id)
	return err
}

func (db *DB) GetUserAPIIDBySecret(ctx context.Context, secret string) (int, error) {
	conn := pgsql.FromContext(ctx)
	query := `SELECT r.id
				FROM user_apis u
				INNER JOIN resources r ON (r.id = u.resource_id and r.type = $2)
				WHERE u.secret = $1`
	id := 0
	err := conn.GetContext(ctx, &id, query, secret, authz.TypeAPIUser)
	return id, err
}

func (db *DB) GetUserAPI(ctx context.Context, id int) (APIShortRow, error) {
	conn := pgsql.FromContext(ctx)

	query := `SELECT r.id,r.name,r.status, r.photo, r.color, u.url, 
              rw.id as workspace_id, rw.ext_id as workspace_ext_id, rw.name as workspace_name, u.secret, u.owner_user_id
				FROM user_apis u
				INNER JOIN resources r ON (r.id = u.resource_id and r.type = $2)
				INNER JOIN resources rw ON (rw.id = r.workspace_id and rw.type = $3)
				WHERE r.id = $1`
	rsp := APIShortRow{}
	err := conn.GetContext(ctx, &rsp, query, id, authz.TypeAPIUser, authz.TypeWorkspace)
	if err != nil {
		return rsp, err
	}
	rsp.Scopes = db.ListUserAPIScopesMap(ctx, id)
	if err != nil {
		return rsp, err
	}
	return rsp, err
}

func (db *DB) ListUserAPIScopesMap(ctx context.Context, id int) map[int][]string {
	conn := pgsql.FromContext(ctx)
	req := struct {
		RoleOwner int `db:"role_owner"`
		authz.DBArgs
		ID int `db:"id"`
	}{}
	req.DBArgs = authz.NewDBArgs()
	req.RoleOwner = authz.RoleOwner
	req.ID = id
	query := `  select  rl.object_type, r.ext_id
                    from rules rl 
                        inner join resources r on(r.id = rl.object_id)
  				   where rl.subject_id = :id 
  				     and rl.subject_type =:type_api_user
                     and rl.relation=:rel_include
                     and (r.type = :type_scopes_control or r.type = :type_scopes_corezoid or r.type = :type_scopes_sa )
                `
	rows, err := conn.NamedQuery(query, req)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	rsp := make(map[int][]string)
	rsp[authz.TypeScopeControl] = make([]string, 0)
	rsp[authz.TypeScopeCorezoid] = make([]string, 0)
	rsp[authz.TypeScopesSA] = make([]string, 0)

	for rows.Next() {
		var typeInt int
		var scope string
		err := rows.Scan(&typeInt, &scope)
		apperr.Error(err)
		rsp[typeInt] = append(rsp[typeInt], scope)
	}
	return rsp
}

func (db *DB) ListUserAPIScopes(ctx context.Context, ids []int) map[int][]Scope {
	conn := pgsql.FromContext(ctx)
	req := struct {
		IDs interface {
			driver.Valuer
			sql.Scanner
		} `db:"ids"`
		RoleOwner int `db:"role_owner"`
		authz.DBArgs
	}{}
	req.DBArgs = authz.NewDBArgs()
	req.IDs = pq.Array(ids)
	req.RoleOwner = authz.RoleOwner
	query := `  select rl.subject_id, rl.object_type, r.ext_id
                    from rules rl inner join resources r on(r.id = rl.object_id) 
  				   where rl.subject_type =:type_api_user
  				     and rl.subject_id = ANY(:ids)
                     and rl.relation=:rel_include
                     and (r.type = :type_scopes_control or r.type = :type_scopes_corezoid or r.type = :type_scopes_sa )
                `
	rows, err := conn.NamedQuery(query, req)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	rsp := make(map[int][]Scope, 0)
	for rows.Next() {
		var apiID, typeInt int
		item := Scope{}
		err := rows.Scan(&apiID, &typeInt, &item.ID)
		item.Type = authz.ResourceNames[typeInt]
		apperr.Error(err)
		if info, ok := rsp[apiID]; ok {
			rsp[apiID] = append(info, item)
		} else {
			rsp[apiID] = []Scope{item}
		}
	}
	return rsp
}

func (db *DB) ListUserAPIs(ctx context.Context, wid, ownerID int, search string, ids []int, limit, offset int, sortBy, orderBy string) []*APIRow {
	conn := pgsql.FromContext(ctx)
	req := struct {
		IDs interface {
			driver.Valuer
			sql.Scanner
		} `db:"ids"`
		authz.DBArgs
		WID     int    `db:"workspace_id"`
		Search  string `db:"search_title"`
		OwnerID int    `db:"owner_id"`
	}{}

	req.DBArgs = authz.NewDBArgs()
	req.Limit = limit
	req.Offset = offset
	req.IDs = pq.Array(ids)
	req.WID = wid
	req.OwnerID = ownerID
	req.Search = pgsql.ToTSV(search)
	rsp := make([]*APIRow, 0)
	query := `SELECT r.id , r.name,r.status, r.photo,r.color,  r.created_at, u.url, 
       				u.owner_user_id, uo.name as owner_name, u.secret, r.meta
            from resources r
              inner join  user_apis u on(r.id = u.resource_id)   
			  inner join  users uo on (uo.id = u.owner_user_id)
			WHERE  r.type = :type_api_user and r.workspace_id = :workspace_id 
           `

	if len(ids) > 0 {
		query += " and r.id  = ANY(:ids)"
	}
	if search != "" {
		query += ` and r.search_title @@ to_tsquery('simple',:search_title)`
	}
	if ownerID > 0 {
		query += ` and u.owner_user_id = :owner_id`
	}
	query += " group by r.id , r.name,r.status, r.photo,r.color,  r.created_at, u.url, u.owner_user_id, uo.name, u.secret, r.meta"

	if sortBy != "" {
		query += " ORDER BY r." + sortBy
		if orderBy != "" {
			query += " " + orderBy
		}
		if sortBy != "id" {
			query += " , r.id "
		}

	} else {
		query += " ORDER BY r.id"
	}

	if limit > 0 || offset > 0 {
		query += " LIMIT :limit OFFSET :offset"
	}

	rows, err := conn.NamedQuery(query, req)

	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		item := APIRow{}
		err := rows.Scan(&item.ID, &item.Name, &item.Status, &item.Photo, &item.Color, &item.CreatedAt, &item.URL,
			&item.OwnerID, &item.OwnerName, &item.Secret, &item.Meta)
		apperr.Error(err)
		rsp = append(rsp, &item)
	}
	return rsp
}

func (db *DB) CreateUserAPIInfo(ctx context.Context, req CreateDBUserAPIRow) error {
	conn := pgsql.FromContext(ctx)
	query := `INSERT INTO user_apis (
					 resource_id, 
					 url,
					 owner_user_id, 
					 secret) 
		       VALUES (
		               :resource_id, 
					   :url,
					   :owner_user_id, 
					   :secret) `
	_, err := conn.NamedExecContext(ctx, query, req)
	return err
}

func (db *DB) Update(ctx context.Context, id int, name string, photo, lang *string) error {
	conn := pgsql.FromContext(ctx)
	var data *string
	if photo != nil {
		data1 := struct {
			Photo *string `json:"photo,omitempty"`
		}{Photo: photo}

		dataBin, err := json.Marshal(data1)
		apperr.Error(err)
		data = ptrutil.Ptr(string(dataBin))
	}
	query := `UPDATE users set data = $1,  name = $2, lang = $4 WHERE id = $3 `
	_, err := conn.ExecContext(ctx, query, data, name, id, lang)
	return err
}

func (db *DB) UpdateAPIUserInfo(ctx context.Context, id int, req CreateUpdateAPIRow) error {
	conn := pgsql.FromContext(ctx)
	query := `UPDATE user_apis set   url = $1 WHERE resource_id = $2`
	_, err := conn.ExecContext(ctx, query, req.URL, id)
	if err != nil {
		return err
	}
	query = `UPDATE resources set name = $1,  status = $2, photo = $3, color = $4, meta = coalesce($5, meta)  WHERE id = $6 and type =  $7`
	_, err = conn.ExecContext(ctx, query, req.Name, req.Status, req.Photo, req.Color, req.Meta, id, authz.TypeAPIUser)
	if err != nil {
		return err
	}
	err = db.Update(ctx, id, req.Name, req.Photo, nil)
	if err != nil {
		return err
	}
	return err
}

func (db *DB) Search(ctx context.Context, wid int, types []int, searchText string, limit, offset int) []SearchRow {
	conn := pgsql.FromContext(ctx)
	req := struct {
		Types any `db:"types"`
		authz.DBArgs
		WID        int    `db:"workspace_id"`
		SearchText string `db:"search_title"`
	}{}
	req.Types = pq.Array(types)
	req.DBArgs = authz.NewDBArgs()
	req.Limit = limit
	req.Offset = offset
	req.WID = wid
	req.SearchText = searchText
	rsp := make([]SearchRow, 0)

	query := `SELECT u.id, u.name,ua.url, u.data, rs.color
            from users u
                 left join resources rs on( u.id =rs.id and rs.type = ANY(:types))
                 left join  user_apis ua on(u.id = ua.resource_id)   
                inner join rules r  on(
                  r.subject_id = u.id and 
                  r.subject_type = ANY(:types)  and 
                  r.workspace_id =:workspace_id)  
               left outer join login_to_users lu on(lu.user_id = u.id and u.id = r.subject_id)
               left outer join logins l on(l.id = lu.login_id)  
              
			 WHERE    
			 	u.search_title @@ to_tsquery('simple',:search_title) OR 
			    to_tsvector('simple', l.login) @@ to_tsquery('simple',:search_title)
		    GROUP by u.id, u.name, ua.url, u.data, rs.color 
            ORDER BY u.id
            LIMIT :limit OFFSET :offset
           `
	rows, err := conn.NamedQuery(query, req)

	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		item := SearchRow{}
		err := rows.Scan(&item.ID, &item.Name, &item.URL, &item.Data, &item.Color)
		apperr.Error(err)
		item.MarshalData()
		rsp = append(rsp, item)
	}

	return rsp
}

func (db *DB) List(ctx context.Context, f Filter, wid, limit, offset int, sortBy, orderBy string) (rsp []*Row, total int) {
	conn := pgsql.FromContext(ctx)
	req := struct {
		RoleIDs []int `db:"role_ids"`
		Types   any   `db:"types"`
		IDs     interface {
			driver.Valuer
			sql.Scanner
		} `db:"ids"`
		authz.DBArgs
		WID    int    `db:"workspace_id"`
		Search string `db:"search_title"`
	}{}
	if len(f.Types) == 0 {
		f.Types = []int{authz.TypeUser}
	}
	req.Types = pq.Array(f.Types)
	req.DBArgs = authz.NewDBArgs()
	req.Limit = limit
	req.Offset = offset
	req.IDs = pq.Array(f.IDs)
	req.WID = wid
	req.RoleIDs = f.RoleIDs
	req.Search = pgsql.ToTSV(f.Search)
	rsp = make([]*Row, 0)

	query := `SELECT u.id, u.name,ua.url, u.create_time, u.last_entrance,u.status, u.data, rs.color, rb.relation as rule_status , count(u.id) OVER() AS full_count
            from users u
              left join resources rs on (u.id =rs.id and rs.type = ANY(:types))
              left join  user_apis ua on(u.id = ua.resource_id)   
              inner join rules r  on(r.subject_id = u.id)
              left  join login_to_users lu on(lu.user_id = u.id and u.id =r.subject_id)
              left  join logins l on(l.id = lu.login_id and u.id = r.subject_id) 
              left  join rules rb  on(
                  rb.subject_id = u.id and 
                  rb.subject_type =:type_user  and 
                  rb.relation=:rel_blocked  and
                  rb.object_type=:type_workspace and 
                  rb.workspace_id =:workspace_id)
			where    
				r.workspace_id =:workspace_id and 
			    r.subject_type =ANY(:types)  
           `

	if len(f.IDs) > 0 {
		query += " and u.id = ANY(:ids)"
	}
	if f.Search != "" {
		query += ` and ( u.search_title @@ to_tsquery('simple',:search_title) OR to_tsvector('simple', l.login) @@ to_tsquery('simple',:search_title) )`
	}
	if len(f.RoleIDs) > 0 {
		query += ` and r.object_id = ANY(:role_ids)`
	}
	query += ` group by u.id, u.name, ua.url, u.create_time, u.last_entrance, u.status, u.data,rs.color, rb.relation`

	if sortBy != "" {
		query += " ORDER BY u." + sortBy
		if orderBy != "" {
			query += " " + orderBy
		}
		if sortBy != "id" {
			query += " , u.id"
		}

	} else {
		query += " ORDER BY u.id"
	}

	if limit > 0 || offset > 0 {
		query += " LIMIT :limit OFFSET :offset"
	}

	rows, err := conn.NamedQuery(query, req)

	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	total = 0
	for rows.Next() {
		item := Row{}
		createdAt := 0
		err := rows.Scan(&item.ID, &item.Name, &item.URL, &createdAt, &item.LastEntrance, &item.Status, &item.Data, &item.Color, &item.RuleStatus, &total)
		apperr.Error(err)
		item.MarshalData()
		item.CreatedAt = time.Unix(int64(createdAt), 0)
		rsp = append(rsp, &item)
	}

	return rsp, total
}

type RowAll struct {
	ID             int              `db:"id"`
	Name           string           `db:"name"`
	LastEntrance   *int             `db:"last_entrance"`
	CreateTime     int              `db:"create_time"`
	Status         authz.StatusUser `db:"status"`
	MFAType        *int             `db:"mfa_type"`
	WorkspaceCount int              `db:"workspace_count"`
	BaseRow
}

func (db *DB) ListAll(ctx context.Context, search string, status *authz.StatusUser, limit, offset int, sortBy, orderBy string) (users []RowAll, total int) {
	conn := pgsql.FromContext(ctx)
	req := struct {
		UID int `db:"user_id"`
		SSO int `db:"sso"`
		authz.DBArgs
		Search string `db:"search_title"`
		Status bool   `db:"status"`
	}{}

	req.DBArgs = authz.NewDBArgs()
	req.Limit = limit
	req.Offset = offset
	req.Search = pgsql.ToTSV(search)
	req.SSO = authz.AuthTypeSSO
	selectQuery := `select u.id, u.name, u.data, u.status,u.create_time,u.last_entrance, mfa.type as mfa_type, 
       				count(distinct rs.id) as workspace_count`
	queryFromPart1 := `	from users u 
				    inner join login_to_users l2u on(u.id = l2u.user_id)
				    inner join logins l on(l.id = l2u.login_id)`
	queryFromPart2 := ` left join  rules r on(r.subject_id = u.id 
				                             and r.subject_type =:type_user 
				                             and r.object_type =:type_role) 
					left join resources rs on (rs.id = r.workspace_id and rs.type =:type_workspace and rs.status !=2 )
					left join user_to_2fa mfa on (mfa.user_id = u.id)`
	queryWhere := ` where 1=1 `
	if search != "" {
		queryWhere += ` and ( u.search_title @@ to_tsquery('simple',:search_title) OR to_tsvector('simple', l.login) @@ to_tsquery('simple',:search_title) )`
	}
	if status != nil {
		req.Status = bool(*status)
		queryWhere += ` and u.status = :status`
	}
	query := selectQuery + queryFromPart1 + queryFromPart2 + queryWhere + ` group by u.id, u.name, u.data, u.status, u.create_time, u.last_entrance,mfa.type`
	if sortBy != "" {
		switch sortBy {
		case "workspace_count":
			query += " ORDER BY count( r.workspace_id)"
		case "created_at":
			query += " ORDER BY u.create_time"
		default:
			query += " ORDER BY u." + sortBy
		}
		if orderBy != "" {
			query += " " + orderBy
		}
		if sortBy != "id" {
			query += " , u.id"
		}
	} else {
		query += " ORDER BY u.id"
	}

	if limit > 0 || offset > 0 {
		query += " LIMIT :limit OFFSET :offset"
	}
	rsp := make([]RowAll, 0)
	rows, err := conn.NamedQuery(query, req)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		item := RowAll{}
		err := rows.Scan(&item.ID, &item.Name, &item.Data, &item.Status, &item.CreateTime, &item.LastEntrance, &item.MFAType, &item.WorkspaceCount)
		apperr.Error(err)
		item.MarshalData()
		rsp = append(rsp, item)
	}
	queryTotal := `SELECT count(distinct u.id) ` + queryFromPart1 + queryWhere

	rows1, err := conn.NamedQuery(queryTotal, req)
	apperr.Error(err)
	defer func() {
		_ = rows1.Close()
	}()
	if rows1.Next() {
		err = rows1.Scan(&total)
		apperr.Error(err)
	}
	return rsp, total
}

func (db *DB) ListByLicense(ctx context.Context, ownerID int, f Filter, limit, offset int, sortBy, orderBy string) (users []*Row, total int) {
	conn := pgsql.FromContext(ctx)
	req := struct {
		UID int `db:"user_id"`
		SSO int `db:"sso"`
		authz.DBArgs
		Search string `db:"search_title"`
	}{}

	req.DBArgs = authz.NewDBArgs()
	req.Limit = limit
	req.Offset = offset
	req.Search = pgsql.ToTSV(f.Search)
	req.SSO = authz.AuthTypeSSO
	req.UID = ownerID

	query := `select subject_id, name,last_entrance,data, color, rule_status `

	where := `from (
                select distinct on (ru.subject_id) ru.subject_id, rb.relation as rule_status , u.name, u.last_entrance, u.data, rs.color, u.search_title, l.login
                  from login_to_users l2u, logins l,  (
                  select workspace_id 
                    from rules 
                   where subject_id = :user_id
                     and subject_type = :type_user
                     and object_type = :type_role
                     and object_id = 1
                     and relation = :rel_member
                  group by workspace_id
              ) rw, rules ru  left join rules rb  on(
                  rb.subject_id = ru.subject_id  
                  and rb.subject_type =:type_user   
                  and rb.relation=:rel_blocked  
                  and rb.object_type=:type_workspace  
                  and rb.workspace_id =ru.workspace_id)
            left join users u  on(ru.subject_id = u.id)
			left join resources rs on (u.id =rs.id and rs.type = :type_user)                                      
			where rw.workspace_id = ru.workspace_id
			  and ru.subject_type =:type_user 
			  and ru.object_type = :type_role
			  and ru.subject_id = l2u.user_id
			  and l2u.login_id = l.id 
			  and l.type!=:sso order by ru.subject_id, rb.relation desc) as l2ul`

	if f.Search != "" {
		where += ` where ( search_title @@ to_tsquery('simple',:search_title) OR to_tsvector('simple', login) @@ to_tsquery('simple',:search_title) )`
	}

	query += where

	if sortBy != "" {
		query += " ORDER BY " + sortBy
		if orderBy != "" {
			query += " " + orderBy
		}
	}

	if limit > 0 || offset > 0 {
		query += " LIMIT :limit OFFSET :offset"
	}
	rsp := make([]*Row, 0)
	rows, err := conn.NamedQuery(query, req)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		item := Row{}
		err := rows.Scan(&item.ID, &item.Name, &item.LastEntrance, &item.Data, &item.Color, &item.RuleStatus)
		apperr.Error(err)
		item.MarshalData()

		rsp = append(rsp, &item)
	}
	if sortBy == "status" {
		sort.Slice(rsp, func(i, j int) bool { // reverse slice
			return true
		})
	}

	total, err = db.listAllTotal(ctx, where, req)
	apperr.Error(err)

	return rsp, total
}

func (db *DB) listAllTotal(ctx context.Context, query string, arg any) (total int, err error) {
	conn := pgsql.FromContext(ctx)
	fields := `SELECT count(*) `

	rows, err := conn.NamedQuery(fields+query, arg)

	if err != nil {
		return 0, err
	}
	defer func() {
		_ = rows.Close()
	}()
	if rows.Next() {
		err = rows.Scan(&total)
		if err != nil {
			return 0, err
		}
	}
	return
}

func (db *DB) GetByToken(ctx context.Context, token string, clientID int) (Row, error) {
	conn := pgsql.FromContext(ctx)
	var rsp Row
	query := `SELECT u.id, u.name, u.data, rs.color
              FROM users u
              left join resources rs on( u.id =rs.id and rs.type = $3)
				inner join  oauth2_tokens t on(t.user_id = u.id)
             WHERE t.access_token = $1 and t.client_id = $2 `
	err := conn.GetContext(ctx, &rsp, query, token, clientID, authz.TypeUser)
	if err != nil {
		return rsp, err
	}
	rsp.MarshalData()
	return rsp, err
}

func (db *DB) GetByID(ctx context.Context, id int) (Row, error) {
	conn := pgsql.FromContext(ctx)
	var rsp Row
	query := `SELECT u.id, u.name,  u.data, rs.color, u.status, u.last_logout, u.create_time, u.updated_status_at,
       CASE WHEN u2.user_id IS NULL THEN false ELSE true END as is_2fa,coalesce(rs.type, 2) as type
              FROM users u 
                  left join resources rs on(u.id =rs.id and (rs.type = $2 or rs.type = $3))
                  left join user_to_2fa u2 on(u.id = u2.user_id)
             WHERE u.id = $1`
	err := conn.GetContext(ctx, &rsp, query, id, authz.TypeUser, authz.TypeAPIUser)
	if err != nil {
		return rsp, err
	}
	rsp.MarshalData()
	return rsp, err
}

func (db *DB) ListByID(ctx context.Context, ids []int) ([]RowForEvent, error) {
	conn := pgsql.FromContext(ctx)
	var rsp []RowForEvent
	query := `SELECT u.id, u.name
              FROM users u 
             WHERE u.id = ANY($1)`
	err := conn.SelectContext(ctx, &rsp, query, pq.Array(ids))
	if err != nil {
		return rsp, err
	}

	return rsp, err
}

func (db *DB) GetLogins(ctx context.Context, userIDs []int) ([]Login, error) {
	conn := pgsql.FromContext(ctx)
	rsp := make([]Login, 0)
	query := `SELECT l.id, l.create_time, l.login, l.type , ltu.user_id, l.hash1
              FROM logins l inner join login_to_users ltu on (l.id = ltu.login_id) 
             WHERE ltu.user_id = ANY($1)`
	err := conn.SelectContext(ctx, &rsp, query, pq.Array(userIDs))
	return rsp, err
}

func (db *DB) GetIDsByDomain(ctx context.Context, domain string) []int {
	conn := pgsql.FromContext(ctx)
	rsp := make([]int, 0)
	query := `	SELECT u.id	
				FROM users u
				INNER JOIN login_to_users ltu ON (u.id = ltu.user_id)
				INNER JOIN logins l ON (l.id = ltu.login_id)
				WHERE l.login like $1`
	err := conn.SelectContext(ctx, &rsp, query, "%@"+domain)
	apperr.Error(err)
	return rsp
}

func (db *DB) GetUsersByLogin(ctx context.Context, lType int, login string) []Short {
	conn := pgsql.FromContext(ctx)
	rsp := make([]Short, 0)
	query := `SELECT u.id, u.name,u.status,  l.hash1, l.login, ltu.login_id, 
       (select created_at from hash_history where login_id = l.id order by id desc limit 1) as hash_created_at, 
       l.blocked_at as login_blocked_at
                FROM users u
                 inner join login_to_users ltu on(u.id = ltu.user_id)
                 inner join logins l on(l.id = ltu.login_id)
               WHERE  l.login = $1`
	args := []interface{}{login}
	if lType > 0 {
		query += " and l.type = $2"
		args = append(args, lType)
	}
	err := conn.SelectContext(ctx, &rsp, query, args...)
	apperr.Error(err)
	return rsp
}

func (db *DB) isOldDB(ctx context.Context) bool {
	conn := pgsql.FromContext(ctx)
	var old bool
	query := `SELECT EXISTS(SELECT FROM information_schema.tables WHERE  table_schema = 'public' AND    table_name   = 'user_groups')`
	err := conn.GetContext(ctx, &old, query)
	apperr.Error(err)
	return old
}

func (db *DB) CreateJustUser(ctx context.Context, name string, data *string, lang string) int {
	conn := pgsql.FromContext(ctx)
	query := "INSERT INTO users (name, data, lang) VALUES ($1, $2, $3) returning id"
	rows, err := conn.QueryContext(ctx, query, name, data, lang)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		var id int
		err := rows.Scan(&id)
		apperr.Error(err)
		return id
	}
	panic("failed to create user")
}

func (db *DB) CreateUser(ctx context.Context, req CreateRow) (loginID, userID int) {
	conn := pgsql.FromContext(ctx)
	query := "INSERT INTO logins (login, type, hash1) VALUES ($1, $2, $3) returning id"
	rows0, err := conn.QueryContext(ctx, query, req.Login, req.LoginType, req.LoginHash1)
	apperr.Error(err)
	defer func() {
		_ = rows0.Close()
	}()
	for rows0.Next() {
		err := rows0.Scan(&loginID)
		apperr.Error(err)
	}
	query = "INSERT INTO users (name, data, lang, last_entrance) VALUES ($1, $2, $3, null) returning id"
	rows1, err := conn.QueryContext(ctx, query, req.UserName, req.UserData, req.UserLang)
	apperr.Error(err)
	defer func() {
		_ = rows1.Close()
	}()
	for rows1.Next() {
		err := rows1.Scan(&userID)
		apperr.Error(err)
	}
	query = "INSERT INTO login_to_users (login_id, user_id) VALUES ($1, $2)"
	_, err = conn.ExecContext(ctx, query, loginID, userID)
	apperr.Error(err)
	db.createPasswordHistory(ctx, loginID, req.LoginType, req.LoginHash1)
	if db.isOldDB(ctx) {
		var groupID int
		query = "INSERT INTO user_groups ( name, type, owner_user_id ) VALUES ( $1, $2, $3 ) returning id"
		rows, err := conn.QueryContext(ctx, query, "root", 1, userID)
		apperr.Error(err)
		defer func() {
			_ = rows.Close()
		}()
		for rows.Next() {
			err := rows.Scan(&groupID)
			apperr.Error(err)
		}
		query = "INSERT INTO user_to_user_groups ( user_id, user_group_id ) VALUES ( $1, $2 )"
		_, err = conn.ExecContext(ctx, query, userID, groupID)
		apperr.Error(err)
	}

	return loginID, userID
}

func (db *DB) UpdateCompanyInfo(ctx context.Context, uid int, name, size string) error {
	conn := pgsql.FromContext(ctx)
	query := `UPDATE users SET company_name = $1, company_size = $2 WHERE id = $3`
	_, err := conn.ExecContext(ctx, query, name, size, uid)
	return err
}

func (db *DB) updateUsersStatus(ctx context.Context, status authz.StatusUser, ids []int) error {
	conn := pgsql.FromContext(ctx)
	r := struct {
		Status bool `db:"status"`
		Users  any  `db:"users"`
	}{
		Status: bool(status),
		Users:  pq.Array(ids),
	}
	q := `update users set status = :status, updated_status_at = now() where id = ANY(:users)`
	_, err := conn.NamedExecContext(ctx, q, r)
	return err
}

func (db *DB) updateAPIOwner(ctx context.Context, wid, oldOwnerID, newOwnerID int) {
	conn := pgsql.FromContext(ctx)
	query := `UPDATE user_apis SET owner_user_id = $1 
                 WHERE resource_id = ANY(SELECT id FROM resources WHERE workspace_id = $2 and type = 5) AND owner_user_id = $3`
	_, err := conn.ExecContext(ctx, query, newOwnerID, wid, oldOwnerID)
	apperr.Error(err)
}

func (db *DB) setLastLogout(ctx context.Context, uid int) {
	conn := pgsql.FromContext(ctx)
	query := `UPDATE users SET last_logout = $1 WHERE id = $2`
	_, err := conn.ExecContext(ctx, query, time.Now().Unix(), uid)
	apperr.Error(err)
}

func (db *DB) deleteAllTokens(ctx context.Context, uid int) {
	conn := pgsql.FromContext(ctx)
	q := `DELETE FROM oauth2_tokens WHERE user_id = $1`
	_, err := conn.ExecContext(ctx, q, uid)
	apperr.Error(err)
}

func (db *DB) bindLoginToUser(ctx context.Context, userID, loginType int, login string, loginHash1 *string) int {
	conn := pgsql.FromContext(ctx)
	q := `INSERT INTO logins (login, type, hash1) VALUES ($1, $2, $3) returning id`
	rows, err := conn.QueryContext(ctx, q, login, loginType, loginHash1)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	id := 0
	for rows.Next() {
		err := rows.Scan(&id)
		apperr.Error(err)
	}
	q = `INSERT INTO login_to_users (login_id, user_id) VALUES ($1, $2)`
	_, err = conn.ExecContext(ctx, q, id, userID)
	apperr.Error(err)
	return id

}

func (db *DB) updateLastEntranceDateAndLang(ctx context.Context, uid int, lang string) {
	conn := pgsql.FromContext(ctx)
	q := `UPDATE users SET last_entrance = $2, lang = $3 WHERE id = $1`
	_, err := conn.ExecContext(ctx, q, uid, time.Now().Unix(), lang)
	apperr.Error(err)
}

func (db *DB) updateUserPassword(ctx context.Context, login string, lType int, pass string) {
	conn := pgsql.FromContext(ctx)
	q := `UPDATE logins SET hash1 = $3 WHERE login = $1 AND type = $2 `
	_, err := conn.ExecContext(ctx, q, login, lType, pass)
	apperr.Error(err)

}

func (db *DB) updateHashCreatedAt(ctx context.Context, id int, hashCreatedAt time.Time) {
	conn := pgsql.FromContext(ctx)
	q := `UPDATE hash_history SET created_at = $2 WHERE login_id = $1`
	_, err := conn.ExecContext(ctx, q, id, hashCreatedAt)
	apperr.Error(err)

}

func (db *DB) createPasswordHistory(ctx context.Context, loginID, lType int, password *string) {
	if lType != authz.AuthTypeSimpleEmail || password == nil {
		return
	}
	conn := pgsql.FromContext(ctx)
	q := `INSERT INTO hash_history(login_id, hash1)values($1,$2)`
	_, err := conn.ExecContext(ctx, q, loginID, password)
	apperr.Error(err)
	q = `DELETE FROM hash_history 
        WHERE id = (
		 SELECT id FROM hash_history WHERE login_id = $1 ORDER BY id DESC LIMIT 1 OFFSET 10)`
	_, err = conn.ExecContext(ctx, q, loginID)
	apperr.Error(err)
}

func (db *DB) GetPasswordsByLogin(ctx context.Context, loginID int) []string {
	conn := pgsql.FromContext(ctx)
	q := `SELECT hash1 FROM hash_history WHERE login_id =$1`
	rows, err := conn.QueryContext(ctx, q, loginID)
	apperr.Error(err)
	defer func() {
		_ = rows.Close()
	}()
	rsp := make([]string, 0)
	for rows.Next() {
		var pass string
		err := rows.Scan(&pass)
		apperr.Error(err)
		rsp = append(rsp, pass)
	}
	return rsp

}

func (db *DB) GetExpiringPasswords(ctx context.Context, dur time.Duration) []Short {
	date := time.Now().Add(-dur)
	conn := pgsql.FromContext(ctx)
	q := `SELECT u.id, u.name,  l.hash1, l.login, lu.login_id, hh.created_at as hash_created_at
           FROM hash_history hh
             inner join logins  l on (l.id = hh.login_id and hh.hash1=l.hash1)
             inner join login_to_users lu on (lu.login_id = l.id)
             inner join users u on (u.id = lu.user_id)
          WHERE hh.created_at<$1 and l.type = 7 and hh.notified_at is null`
	rsp := make([]Short, 0)
	err := conn.SelectContext(ctx, &rsp, q, date)
	apperr.Error(err)
	return rsp
}
func (db *DB) SetNotified(ctx context.Context, loginID int, hash1 string) {
	conn := pgsql.FromContext(ctx)
	q := `UPDATE hash_history SET notified_at = $1 WHERE login_id = $2 and hash1 = $3`
	_, err := conn.ExecContext(ctx, q, time.Now(), loginID, hash1)
	apperr.Error(err)
}

func (db *DB) SetBlocked(ctx context.Context, loginID int, hash1 string) {
	conn := pgsql.FromContext(ctx)
	q := `UPDATE hash_history SET blocked_at = $1 WHERE login_id = $2 and hash1 = $3`
	_, err := conn.ExecContext(ctx, q, time.Now(), loginID, hash1)
	apperr.Error(err)
}

func (db *DB) BlockLogin(ctx context.Context, loginID int) {
	conn := pgsql.FromContext(ctx)
	q := `UPDATE logins SET blocked_at = $1  WHERE id = $2`
	_, err := conn.ExecContext(ctx, q, time.Now(), loginID)
	apperr.Error(err)

}

func (db *DB) unblockLogin(ctx *gin.Context, id int) {
	conn := pgsql.FromContext(ctx)
	q := `UPDATE logins SET blocked_at = null WHERE id = $1`
	_, err := conn.ExecContext(ctx, q, id)
	apperr.Error(err)
}

func (db *DB) ListUserTokens(ctx context.Context, userID int) ([]UserToken, error) {
	conn := pgsql.FromContext(ctx)
	query := `SELECT id, user_id, name, description, token, expire_at, 
		CASE WHEN expire_at < NOW() THEN 'expired' ELSE status::text END as status,
		created_at, updated_at 
		FROM user_tokens WHERE user_id = $1 ORDER BY created_at DESC`
	
	var tokens []UserToken
	err := conn.SelectContext(ctx, &tokens, query, userID)
	if err != nil {
		return nil, err
	}
	return tokens, nil
}

func (db *DB) CreateUserToken(ctx context.Context, userID int, req UserTokenCreateReq, token string) (*UserToken, error) {
	conn := pgsql.FromContext(ctx)
	query := `INSERT INTO user_tokens (user_id, name, description, token, expire_at) 
		VALUES ($1, $2, $3, $4, $5) RETURNING id, user_id, name, description, token, expire_at, status, created_at, updated_at`
	
	var result UserToken
	err := conn.GetContext(ctx, &result, query, userID, req.Name, req.Description, token, req.ExpireAt)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (db *DB) UpdateUserToken(ctx context.Context, tokenID string, userID int, req UserTokenUpdateReq) (*UserToken, error) {
	conn := pgsql.FromContext(ctx)
	
	setParts := []string{"updated_at = NOW()"}
	args := []interface{}{tokenID, userID}
	argIndex := 3
	
	if req.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *req.Name)
		argIndex++
	}
	if req.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *req.Description)
		argIndex++
	}
	if req.Status != nil && *req.Status == "revoked" {
		setParts = append(setParts, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *req.Status)
		argIndex++
	}
	
	query := fmt.Sprintf(`UPDATE user_tokens SET %s 
		WHERE id = $1 AND user_id = $2 AND status = 'active'
		RETURNING id, user_id, name, description, token, expire_at, status, created_at, updated_at`,
		strings.Join(setParts, ", "))
	
	var result UserToken
	err := conn.GetContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
