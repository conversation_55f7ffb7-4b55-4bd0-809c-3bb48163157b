package aws_test

import (
	"testing"

	"github.com/stretchr/testify/require"
	"sa/pkg/aws"
)

func TestProviderPreload_NoProvider(t *testing.T) {
	t.Setenv("EXTERNAL_SECRET_PROVIDER", "")
	err := aws.ProviderPreload()
	require.NoError(t, err, "Expected no error when no provider is set")
}

func TestProviderPreload_UnsupportedProvider(t *testing.T) {
	t.Setenv("EXTERNAL_SECRET_PROVIDER", "unsupported")

	err := aws.ProviderPreload()
	require.NoError(t, err, "Expected no error for unsupported provider")
}

func TestProviderPreloadAWS_NoSecretNames(t *testing.T) {
	t.Setenv("EXTERNAL_SECRET_PROVIDER", "aws")
	t.Setenv("AWS_SECRET_NAMES", "")

	err := aws.ProviderPreload()
	require.NoError(t, err, "Expected no error when AWS_SECRET_NAMES is empty")
}

func TestProviderPreloadAWS_EmptySecretName(t *testing.T) {
	t.Setenv("EXTERNAL_SECRET_PROVIDER", "aws")
	t.Setenv("AWS_SECRET_NAMES", " , , ")

	err := aws.ProviderPreload()
	require.NoError(t, err, "Expected no error when AWS_SECRET_NAMES contains only empty values")
}

func TestProviderPreloadAWS_DefaultRegion(t *testing.T) {
	t.Setenv("AWS_SECRET_REGION", "")
	t.Setenv("EXTERNAL_SECRET_PROVIDER", "aws")
	t.Setenv("AWS_SECRET_NAMES", "")

	err := aws.ProviderPreload()
	require.NoError(t, err, "Expected no error with default region")
}
