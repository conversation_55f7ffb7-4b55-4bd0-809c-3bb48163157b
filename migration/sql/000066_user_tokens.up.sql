CREATE TYPE user_token_status AS ENUM ('active', 'revoked', 'expired');

CREATE TABLE IF NOT EXISTS public.user_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    token VARCHAR(255) NOT NULL UNIQUE,
    expire_at TIMESTAMP NOT NULL,
    status user_token_status NOT NULL DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_user_tokens_user_id ON public.user_tokens(user_id);
CREATE INDEX idx_user_tokens_token ON public.user_tokens(token);
CREATE INDEX idx_user_tokens_status ON public.user_tokens(status);
CREATE INDEX idx_user_tokens_created_at ON public.user_tokens(created_at DESC);

ALTER TABLE public.user_tokens ADD CONSTRAINT check_status_not_manually_expired 
    CHECK (status != 'expired' OR expire_at < NOW());
